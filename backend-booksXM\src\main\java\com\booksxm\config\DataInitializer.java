package com.booksxm.config;

import com.booksxm.entity.Book;
import com.booksxm.entity.User;
import com.booksxm.repository.BookRepository;
import com.booksxm.repository.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Component
public class DataInitializer implements CommandLineRunner {

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private BookRepository bookRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Override
    public void run(String... args) throws Exception {
        // 初始化用户数据
        if (userRepository.count() == 0) {
            // 创建管理员用户
            User admin = new User();
            admin.setUsername("admin");
            admin.setEmail("<EMAIL>");
            admin.setPassword(passwordEncoder.encode("admin123"));
            admin.setRole(User.Role.ADMIN);
            admin.setIsActive(true);
            admin.setEmailVerified(true);
            admin.setCreatedAt(LocalDateTime.now());
            admin.setUpdatedAt(LocalDateTime.now());
            userRepository.save(admin);

            // 创建普通用户
            User user = new User();
            user.setUsername("user");
            user.setEmail("<EMAIL>");
            user.setPassword(passwordEncoder.encode("user123"));
            user.setRole(User.Role.USER);
            user.setIsActive(true);
            user.setEmailVerified(true);
            user.setCreatedAt(LocalDateTime.now());
            user.setUpdatedAt(LocalDateTime.now());
            userRepository.save(user);

            System.out.println("用户数据初始化完成");
        }

        // 初始化图书数据
        if (bookRepository.count() == 0) {
            // 创建示例图书
            Book book1 = new Book();
            book1.setTitle("Java编程思想");
            book1.setAuthor("Bruce Eckel");
            book1.setPublisher("机械工业出版社");
            book1.setIsbn("9787111213826");
            book1.setCategory("编程");
            book1.setLanguage("中文");
            book1.setPrice(new BigDecimal("89.00"));
            book1.setOriginalPrice(new BigDecimal("99.00"));
            book1.setStock(50);
            book1.setPageCount(880);
            book1.setPublishDate("2007-06-01");
            book1.setCoverUrl("/api/covers/books1.png");
            book1.setDescription("Java编程经典教材，深入浅出地介绍Java语言的核心概念和编程技巧。");
            book1.setIsFeatured(true);
            book1.setIsActive(true);
            book1.setSalesCount(120);
            book1.setViewCount(1500);
            book1.setRating(4.8);
            book1.setRatingCount(89);
            book1.setCreatedAt(LocalDateTime.now());
            book1.setUpdatedAt(LocalDateTime.now());
            bookRepository.save(book1);

            Book book2 = new Book();
            book2.setTitle("Spring Boot实战");
            book2.setAuthor("Craig Walls");
            book2.setPublisher("人民邮电出版社");
            book2.setIsbn("9787115419087");
            book2.setCategory("编程");
            book2.setLanguage("中文");
            book2.setPrice(new BigDecimal("79.00"));
            book2.setOriginalPrice(new BigDecimal("89.00"));
            book2.setStock(30);
            book2.setPageCount(312);
            book2.setPublishDate("2016-09-01");
            book2.setCoverUrl("/api/covers/books2.png");
            book2.setDescription("Spring Boot框架实战指南，帮助开发者快速构建企业级应用。");
            book2.setIsFeatured(true);
            book2.setIsActive(true);
            book2.setSalesCount(95);
            book2.setViewCount(1200);
            book2.setRating(4.6);
            book2.setRatingCount(67);
            book2.setCreatedAt(LocalDateTime.now());
            book2.setUpdatedAt(LocalDateTime.now());
            bookRepository.save(book2);

            Book book3 = new Book();
            book3.setTitle("算法导论");
            book3.setAuthor("Thomas H. Cormen");
            book3.setPublisher("机械工业出版社");
            book3.setIsbn("9787111407010");
            book3.setCategory("计算机科学");
            book3.setLanguage("中文");
            book3.setPrice(new BigDecimal("128.00"));
            book3.setOriginalPrice(new BigDecimal("148.00"));
            book3.setStock(25);
            book3.setPageCount(1312);
            book3.setPublishDate("2013-01-01");
            book3.setCoverUrl("https://img3m9.ddimg.cn/96/11/22927209-1_b_1739414348.jpg");
            book3.setDescription("计算机算法经典教材，涵盖了算法设计与分析的各个方面。");
            book3.setIsFeatured(false);
            book3.setIsActive(true);
            book3.setSalesCount(78);
            book3.setViewCount(980);
            book3.setRating(4.9);
            book3.setRatingCount(45);
            book3.setCreatedAt(LocalDateTime.now());
            book3.setUpdatedAt(LocalDateTime.now());
            bookRepository.save(book3);

            Book book4 = new Book();
            book4.setTitle("Vue.js实战");
            book4.setAuthor("梁灏");
            book4.setPublisher("清华大学出版社");
            book4.setIsbn("9787302456469");
            book4.setCategory("前端开发");
            book4.setLanguage("中文");
            book4.setPrice(new BigDecimal("69.00"));
            book4.setOriginalPrice(new BigDecimal("79.00"));
            book4.setStock(40);
            book4.setPageCount(368);
            book4.setPublishDate("2017-03-01");
            book4.setCoverUrl("https://img3m6.ddimg.cn/32/10/25180286-1_b_3.jpg");
            book4.setDescription("Vue.js框架实战教程，从基础到进阶全面讲解Vue.js开发。");
            book4.setIsFeatured(true);
            book4.setIsActive(true);
            book4.setSalesCount(156);
            book4.setViewCount(2100);
            book4.setRating(4.7);
            book4.setRatingCount(123);
            book4.setCreatedAt(LocalDateTime.now());
            book4.setUpdatedAt(LocalDateTime.now());
            bookRepository.save(book4);

            Book book5 = new Book();
            book5.setTitle("深入理解计算机系统");
            book5.setAuthor("Randal E. Bryant");
            book5.setPublisher("机械工业出版社");
            book5.setIsbn("9787111544937");
            book5.setCategory("计算机科学");
            book5.setLanguage("中文");
            book5.setPrice(new BigDecimal("139.00"));
            book5.setOriginalPrice(new BigDecimal("159.00"));
            book5.setStock(20);
            book5.setPageCount(736);
            book5.setPublishDate("2016-11-01");
            book5.setCoverUrl("https://img3m0.ddimg.cn/61/15/11739890410-1_b_1713088713.jpg");
            book5.setDescription("计算机系统经典教材，深入讲解计算机系统的各个层面。");
            book5.setIsFeatured(false);
            book5.setIsActive(true);
            book5.setSalesCount(89);
            book5.setViewCount(1350);
            book5.setRating(4.8);
            book5.setRatingCount(76);
            book5.setCreatedAt(LocalDateTime.now());
            book5.setUpdatedAt(LocalDateTime.now());
            bookRepository.save(book5);

            System.out.println("图书数据初始化完成");
        }
    }
}
