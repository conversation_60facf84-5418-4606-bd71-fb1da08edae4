package com.booksxm.repository;

import com.booksxm.entity.Order;
import com.booksxm.entity.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface OrderRepository extends JpaRepository<Order, Long> {

       /**
        * 根据订单号查找订单
        */
       Optional<Order> findByOrderNumber(String orderNumber);

       /**
        * 根据用户查找订单
        */
       Page<Order> findByUser(User user, Pageable pageable);

       /**
        * 根据用户ID查找订单
        */
       Page<Order> findByUserId(Long userId, Pageable pageable);

       /**
        * 根据订单状态查找订单
        */
       Page<Order> findByStatus(Order.OrderStatus status, Pageable pageable);

       /**
        * 根据支付状态查找订单
        */
       Page<Order> findByPaymentStatus(Order.PaymentStatus paymentStatus, Pageable pageable);

       /**
        * 根据用户和订单状态查找订单
        */
       Page<Order> findByUserAndStatus(User user, Order.OrderStatus status, Pageable pageable);

       /**
        * 根据用户ID和订单状态查找订单
        */
       Page<Order> findByUserIdAndStatus(Long userId, Order.OrderStatus status, Pageable pageable);

       /**
        * 查找指定时间段内的订单
        */
       Page<Order> findByCreatedAtBetween(LocalDateTime startDate, LocalDateTime endDate, Pageable pageable);

       /**
        * 多条件查询订单
        */
       @Query("SELECT o FROM Order o WHERE " +
                     "(:userId IS NULL OR o.user.id = :userId) AND " +
                     "(:status IS NULL OR o.status = :status) AND " +
                     "(:paymentStatus IS NULL OR o.paymentStatus = :paymentStatus) AND " +
                     "(:startDate IS NULL OR o.createdAt >= :startDate) AND " +
                     "(:endDate IS NULL OR o.createdAt <= :endDate) AND " +
                     "(:orderNumber IS NULL OR o.orderNumber LIKE %:orderNumber%)")
       Page<Order> findOrdersWithFilters(@Param("userId") Long userId,
                     @Param("status") Order.OrderStatus status,
                     @Param("paymentStatus") Order.PaymentStatus paymentStatus,
                     @Param("startDate") LocalDateTime startDate,
                     @Param("endDate") LocalDateTime endDate,
                     @Param("orderNumber") String orderNumber,
                     Pageable pageable);

       /**
        * 统计用户的订单数量
        */
       Long countByUser(User user);

       /**
        * 统计用户指定状态的订单数量
        */
       Long countByUserAndStatus(User user, Order.OrderStatus status);

       /**
        * 统计指定状态的订单数量
        */
       Long countByStatus(Order.OrderStatus status);

       /**
        * 统计指定时间段内的订单数量
        */
       @Query("SELECT COUNT(o) FROM Order o WHERE o.createdAt BETWEEN :startDate AND :endDate")
       Long countOrdersByCreatedAtBetween(@Param("startDate") LocalDateTime startDate,
                     @Param("endDate") LocalDateTime endDate);

       /**
        * 统计指定时间段内的销售总额
        */
       @Query("SELECT COALESCE(SUM(o.finalAmount), 0) FROM Order o WHERE " +
                     "o.status IN ('PAID', 'SHIPPED', 'DELIVERED') AND " +
                     "o.createdAt BETWEEN :startDate AND :endDate")
       BigDecimal getTotalSalesByDateRange(@Param("startDate") LocalDateTime startDate,
                     @Param("endDate") LocalDateTime endDate);

       /**
        * 统计总销售额
        */
       @Query("SELECT COALESCE(SUM(o.finalAmount), 0) FROM Order o WHERE o.status IN ('PAID', 'SHIPPED', 'DELIVERED')")
       BigDecimal getTotalSales();

       /**
        * 获取每日销售统计
        */
       @Query("SELECT DATE(o.createdAt) as orderDate, COUNT(o) as orderCount, SUM(o.finalAmount) as totalAmount " +
                     "FROM Order o WHERE o.status IN ('PAID', 'SHIPPED', 'DELIVERED') AND " +
                     "o.createdAt BETWEEN :startDate AND :endDate " +
                     "GROUP BY DATE(o.createdAt) ORDER BY orderDate")
       List<Object[]> getDailySalesStats(@Param("startDate") LocalDateTime startDate,
                     @Param("endDate") LocalDateTime endDate);

       /**
        * 获取月度销售统计
        */
       @Query("SELECT YEAR(o.createdAt) as year, MONTH(o.createdAt) as month, " +
                     "COUNT(o) as orderCount, SUM(o.finalAmount) as totalAmount " +
                     "FROM Order o WHERE o.status IN ('PAID', 'SHIPPED', 'DELIVERED') AND " +
                     "o.createdAt BETWEEN :startDate AND :endDate " +
                     "GROUP BY YEAR(o.createdAt), MONTH(o.createdAt) " +
                     "ORDER BY year, month")
       List<Object[]> getMonthlySalesStats(@Param("startDate") LocalDateTime startDate,
                     @Param("endDate") LocalDateTime endDate);

       /**
        * 查找待处理的订单（已付款但未发货）
        */
       List<Order> findByStatusOrderByCreatedAtAsc(Order.OrderStatus status);

       /**
        * 查找超时未付款的订单
        */
       @Query("SELECT o FROM Order o WHERE o.status = 'PENDING' AND o.createdAt < :timeoutDate")
       List<Order> findTimeoutOrders(@Param("timeoutDate") LocalDateTime timeoutDate);

       /**
        * 获取用户最近的订单
        */
       List<Order> findTop5ByUserOrderByCreatedAtDesc(User user);

       /**
        * 获取最近的订单
        */
       List<Order> findTop10ByOrderByCreatedAtDesc();
}
