package com.booksxm.controller;

import com.booksxm.dto.UserInfoDTO;
import com.booksxm.entity.Address;
import com.booksxm.service.AuthService;
import com.booksxm.service.UserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/user")
@CrossOrigin(origins = "*", maxAge = 3600)
public class UserController {

    private static final Logger logger = LoggerFactory.getLogger(UserController.class);

    @Autowired
    private UserService userService;

    @Autowired
    private AuthService authService;

    /**
     * 获取用户信息
     */
    @GetMapping("/info")
    public ResponseEntity<?> getUserInfo() {
        try {
            Long userId = authService.getCurrentUser().getId();
            UserInfoDTO userInfo = userService.getUserInfo(userId);
            return ResponseEntity.ok(userInfo);
        } catch (Exception e) {
            logger.error("获取用户信息失败: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(Map.of("message", "获取用户信息失败: " + e.getMessage()));
        }
    }

    /**
     * 更新用户信息
     */
    @PutMapping("/info")
    public ResponseEntity<?> updateUserInfo(@RequestBody Map<String, Object> userInfo) {
        try {
            Long userId = authService.getCurrentUser().getId();
            UserInfoDTO updatedUserInfo = userService.updateUserInfo(userId, userInfo);
            return ResponseEntity.ok(updatedUserInfo);
        } catch (Exception e) {
            logger.error("更新用户信息失败: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(Map.of("message", "更新用户信息失败: " + e.getMessage()));
        }
    }

    /**
     * 修改密码
     */
    @PutMapping("/password")
    public ResponseEntity<?> changePassword(@RequestBody Map<String, String> passwordData) {
        try {
            Long userId = authService.getCurrentUser().getId();
            String oldPassword = passwordData.get("oldPassword");
            String newPassword = passwordData.get("newPassword");

            if (oldPassword == null || newPassword == null) {
                return ResponseEntity.badRequest()
                        .body(Map.of("message", "原密码和新密码不能为空"));
            }

            userService.changePassword(userId, oldPassword, newPassword);
            return ResponseEntity.ok(Map.of("message", "密码修改成功"));
        } catch (Exception e) {
            logger.error("修改密码失败: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(Map.of("message", "修改密码失败: " + e.getMessage()));
        }
    }

    /**
     * 获取用户地址列表
     */
    @GetMapping("/addresses")
    public ResponseEntity<?> getAddresses() {
        try {
            Long userId = authService.getCurrentUser().getId();
            List<Address> addresses = userService.getUserAddresses(userId);
            return ResponseEntity.ok(addresses);
        } catch (Exception e) {
            logger.error("获取地址列表失败: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(Map.of("message", "获取地址列表失败: " + e.getMessage()));
        }
    }

    /**
     * 添加地址
     */
    @PostMapping("/addresses")
    public ResponseEntity<?> addAddress(@RequestBody Address address) {
        try {
            Long userId = authService.getCurrentUser().getId();
            Address savedAddress = userService.addAddress(userId, address);
            return ResponseEntity.ok(savedAddress);
        } catch (Exception e) {
            logger.error("添加地址失败: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(Map.of("message", "添加地址失败: " + e.getMessage()));
        }
    }

    /**
     * 更新地址
     */
    @PutMapping("/addresses/{id}")
    public ResponseEntity<?> updateAddress(@PathVariable Long id, @RequestBody Address address) {
        try {
            Long userId = authService.getCurrentUser().getId();
            Address updatedAddress = userService.updateAddress(userId, id, address);
            return ResponseEntity.ok(updatedAddress);
        } catch (Exception e) {
            logger.error("更新地址失败: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(Map.of("message", "更新地址失败: " + e.getMessage()));
        }
    }

    /**
     * 删除地址
     */
    @DeleteMapping("/addresses/{id}")
    public ResponseEntity<?> deleteAddress(@PathVariable Long id) {
        try {
            Long userId = authService.getCurrentUser().getId();
            userService.deleteAddress(userId, id);
            return ResponseEntity.ok(Map.of("message", "地址删除成功"));
        } catch (Exception e) {
            logger.error("删除地址失败: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(Map.of("message", "删除地址失败: " + e.getMessage()));
        }
    }

    /**
     * 获取用户资料（兼容性接口）
     */
    @GetMapping("/profile")
    public ResponseEntity<?> getProfile() {
        try {
            Long userId = authService.getCurrentUser().getId();
            UserInfoDTO userInfo = userService.getUserInfo(userId);
            return ResponseEntity.ok(userInfo);
        } catch (Exception e) {
            logger.error("获取用户资料失败: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(Map.of("message", "获取用户资料失败: " + e.getMessage()));
        }
    }

    /**
     * 更新用户资料（兼容性接口）
     */
    @PutMapping("/profile")
    public ResponseEntity<?> updateProfile(@RequestBody Map<String, Object> userInfo) {
        try {
            Long userId = authService.getCurrentUser().getId();
            UserInfoDTO updatedUserInfo = userService.updateUserInfo(userId, userInfo);
            return ResponseEntity.ok(updatedUserInfo);
        } catch (Exception e) {
            logger.error("更新用户资料失败: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(Map.of("message", "更新用户资料失败: " + e.getMessage()));
        }
    }
}
