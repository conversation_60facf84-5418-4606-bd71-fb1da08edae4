<template>
  <div class="books-page">
    <div class="container">
      <!-- 页面标题和筛选 -->
      <div class="page-header">
        <div class="page-title-section">
          <h1 class="page-title">图书列表</h1>
          <p class="page-subtitle" v-if="currentCategory">{{ currentCategory }}</p>
        </div>

        <div class="header-actions">
          <el-input v-model="searchKeyword" placeholder="搜索图书..." size="large" clearable @keyup.enter="handleSearch"
            @clear="handleSearch" class="search-input">
            <template #append>
              <el-button @click="handleSearch">
                <el-icon>
                  <Search />
                </el-icon>
              </el-button>
            </template>
          </el-input>

          <el-button @click="showFilters = !showFilters" size="large">
            <el-icon>
              <Filter />
            </el-icon>
            筛选
          </el-button>
        </div>
      </div>

      <!-- 筛选面板 -->
      <el-collapse-transition>
        <div v-show="showFilters" class="filters-panel">
          <div class="filters-row">
            <div class="filter-group">
              <label>分类：</label>
              <!-- <el-select v-model="filters.category" placeholder="选择分类" clearable @change="applyFilters" class="ExpandBox">
                <el-option v-for="category in categories" :key="category" :label="category" :value="category" />
              </el-select> -->
              <el-select v-model="filters.category" placeholder="选择分类" @change="applyFilters" clearable class="ExpandBox">
                <el-option label="全部" value="" />
                <el-option v-for="category in categories" :key="category" :label="category" :value="category" />
              </el-select>
            </div>

            <div class="filter-group">
              <label>价格范围：</label>
              <el-input-number v-model="filters.minPrice" placeholder="最低价" :min="0" :precision="2"
                @change="applyFilters" />
              <span>-</span>
              <el-input-number v-model="filters.maxPrice" placeholder="最高价" :min="0" :precision="2"
                @change="applyFilters" />
            </div>

            <div class="filter-group">
              <label>排序：</label>
              <el-select v-model="sortBy" @change="applyFilters" class="ExpandBox">
                <el-option label="最新" value="createdAt-desc" />
                <el-option label="价格从低到高" value="price-asc" />
                <el-option label="价格从高到低" value="price-desc" />
                <el-option label="销量最高" value="salesCount-desc" />
                <el-option label="评分最高" value="rating-desc" />
              </el-select>
            </div>

            <div class="filter-group">
              <el-checkbox v-model="filters.inStock" @change="applyFilters">
                仅显示有库存
              </el-checkbox>
            </div>
          </div>
        </div>
      </el-collapse-transition>

      <!-- 结果统计 -->
      <div class="results-info">
        <span>共找到 {{ totalItems }} 本图书</span>
        <div class="view-mode">
          <el-radio-group v-model="viewMode" size="small">
            <el-radio-button value="grid">
              <el-icon>
                <Grid />
              </el-icon>
            </el-radio-button>
            <el-radio-button value="list">
              <el-icon>
                <List />
              </el-icon>
            </el-radio-button>
          </el-radio-group>
        </div>
      </div>

      <!-- 图书列表 -->
      <div class="books-content" v-loading="loading">
        <div v-if="books.length === 0 && !loading" class="empty-state">
          <el-empty description="暂无图书数据" />
        </div>

        <div v-else :class="['books-container', viewMode]">
          <BookCard v-for="book in books" :key="book.id" :book="book" :view-mode="viewMode"
            @add-to-cart="handleAddToCart" />
        </div>
      </div>
      <div>12345</div>
      <!-- 分页 当总页数 > 1 时，文本会随分页组件一起显示-->
      <div class="pagination-wrapper" v-if="totalPages > 1">
        <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[12, 24, 48, 96]"
          :total="totalItems" layout="total, sizes, prev, pager, next, jumper" @size-change="handleSizeChange"
          @current-change="handlePageChange" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, computed, onMounted, watch } from 'vue'
  import { useRoute, useRouter } from 'vue-router'
  import { useCartStore } from '@/stores/cart'
  import { bookAPI } from '@/api'
  import { ElMessage } from 'element-plus'
  import BookCard from '@/components/BookCard.vue'
  import type { Book } from '@/types'

  const route = useRoute()
  const router = useRouter()
  const cartStore = useCartStore()

  // 响应式数据
  const books = ref<Book[]>([])
  // const categories = ref([])
  const categories = ref<string[]>([])
  const loading = ref(false)
  const showFilters = ref(false)
  const searchKeyword = ref('')
  const viewMode = ref('grid')
  const sortBy = ref('createdAt-desc')

  // 分页数据
  const currentPage = ref(1)  // 当前页码，默认第1页
  const pageSize = ref(12)  // 每页条数，默认12条
  const totalItems = ref(0)  // 总数据条数，需从接口获取后赋值
  const totalPages = ref(0)

  // 筛选条件
  const filters = reactive({
    category: '',
    minPrice: null,
    maxPrice: null,
    inStock: false
  })

  // 计算属性
  const currentCategory = computed(() => {
    return route.query.category as string || filters.category
  })

  // 获取图书列表
  const fetchBooks = async () => {
    try {
      loading.value = true

      const [sortField, sortDirection] = sortBy.value.split('-')

      const params = {
        page: currentPage.value - 1,
        size: pageSize.value,
        sortBy: sortField,
        sortDir: sortDirection,
        search: searchKeyword.value || undefined,
        category: filters.category || undefined,
        minPrice: filters.minPrice || undefined,
        maxPrice: filters.maxPrice || undefined,
        inStock: filters.inStock || undefined
      }

      const response = await bookAPI.getBooks(params)
      const data = response.data || response
      books.value = data.books || data.content || []
      totalItems.value = data.totalItems || data.totalElements || 0
      totalPages.value = data.totalPages || 0

    } catch (error) {
      console.error('获取图书列表失败:', error)
      ElMessage.error('获取图书列表失败')
    } finally {
      loading.value = false
    }
  }

  // 获取分类列表
  // const fetchCategories = async () => {
  //   try {
  //     const response = await bookAPI.getCategories()
  //     categories.value = response.data || []
  //   } catch (error) {
  //     console.error('获取分类失败:', error)
  //   }
  // }
    // 获取分类列表
  const fetchCategories = async () => {
    try {
      const response = await bookAPI.getCategories()
      categories.value = (response as any) || ['文学', '科技', '历史', '艺术', '教育', '生活']
    } catch (error) {
      console.error('获取分类失败:', error)
      categories.value = ['文学', '科技', '历史', '艺术', '教育', '生活']
    }
  }

  // 搜索处理
  const handleSearch = () => {
    currentPage.value = 1
    fetchBooks()
  }

  // 应用筛选
  const applyFilters = () => {
    currentPage.value = 1
    fetchBooks()
  }

  // 分页处理
  const handlePageChange = (page: number) => {
    currentPage.value = page
    fetchBooks()
    // 滚动到顶部
    window.scrollTo({ top: 0, behavior: 'smooth' })
  }

  const handleSizeChange = (size: number) => {
    pageSize.value = size
    currentPage.value = 1
    fetchBooks()
  }

  // 添加到购物车
  const handleAddToCart = async (book: any) => {
    try {
      await cartStore.addItem(book, 1)
      ElMessage.success('已添加到购物车')
    } catch (error) {
      ElMessage.error('添加到购物车失败')
    }
  }

  // 监听路由变化
  watch(() => route.query, (newQuery) => {
    if (newQuery.category) {
      filters.category = newQuery.category as string
    }
    if (newQuery.search) {
      searchKeyword.value = newQuery.search as string
    }
    fetchBooks()
  }, { immediate: true })

  // 页面初始化
  onMounted(() => {
    fetchCategories()

    // 从路由参数初始化筛选条件
    if (route.query.category) {
      filters.category = route.query.category as string
    }
    if (route.query.search) {
      searchKeyword.value = route.query.search as string
    }
  })
</script>

<style scoped>
  .books-page {
    min-height: 100vh;
    padding: 20px 0;
  }

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 24px;
    gap: 20px;
  }

  .page-title-section h1 {
    font-size: 32px;
    font-weight: 600;
    color: #303133;
    margin-bottom: 8px;
  }

  .page-subtitle {
    color: #909399;
    font-size: 16px;
  }

  .header-actions {
    display: flex;
    gap: 12px;
    align-items: center;
  }

  .search-input {
    width: 300px;
  }

  .filters-panel {
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 24px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  }

  .filters-row {
    display: flex;
    gap: 24px;
    align-items: center;
    flex-wrap: wrap;
  }

  .filter-group {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .filter-group label {
    font-weight: 500;
    color: #606266;
    white-space: nowrap;
  }
  .ExpandBox{
    width: 130px;
  }
  .results-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    color: #606266;
  }

  .books-container.grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 24px;
  }

  .books-container.list {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .empty-state {
    text-align: center;
    padding: 60px 20px;
  }

  .pagination-wrapper {
    display: flex;
    justify-content: center;
    margin-top: 40px;
  }

  @media (max-width: 768px) {
    .page-header {
      flex-direction: column;
      align-items: stretch;
    }

    .header-actions {
      flex-direction: column;
    }

    .search-input {
      width: 100%;
    }

    .filters-row {
      flex-direction: column;
      align-items: stretch;
      gap: 16px;
    }

    .filter-group {
      flex-direction: column;
      align-items: stretch;
    }

    .results-info {
      flex-direction: column;
      gap: 12px;
      align-items: stretch;
    }

    .books-container.grid {
      grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
      gap: 16px;
    }
  }
</style>
