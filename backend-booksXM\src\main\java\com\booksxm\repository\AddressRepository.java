package com.booksxm.repository;

import com.booksxm.entity.Address;
import com.booksxm.entity.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface AddressRepository extends JpaRepository<Address, Long> {

    /**
     * 根据用户查找地址列表
     */
    List<Address> findByUserOrderByIsDefaultDescCreatedAtDesc(User user);

    /**
     * 根据用户ID查找地址列表
     */
    List<Address> findByUserIdOrderByIsDefaultDescCreatedAtDesc(Long userId);

    /**
     * 根据用户查找默认地址
     */
    Optional<Address> findByUserAndIsDefaultTrue(User user);

    /**
     * 根据用户ID查找默认地址
     */
    Optional<Address> findByUserIdAndIsDefaultTrue(Long userId);

    /**
     * 根据用户和地址ID查找地址
     */
    Optional<Address> findByIdAndUserId(Long id, Long userId);

    /**
     * 清除用户的所有默认地址标记
     */
    @Modifying
    @Query("UPDATE Address a SET a.isDefault = false WHERE a.user.id = :userId")
    void clearDefaultByUserId(@Param("userId") Long userId);

    /**
     * 统计用户的地址数量
     */
    Long countByUserId(Long userId);
}
