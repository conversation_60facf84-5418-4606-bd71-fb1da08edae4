package com.booksxm.controller;

import com.booksxm.entity.Order;
import com.booksxm.service.OrderService;
import com.booksxm.service.AuthService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/orders")
@CrossOrigin(origins = "*")
public class OrderController {

    private static final Logger logger = LoggerFactory.getLogger(OrderController.class);

    @Autowired
    private OrderService orderService;

    @Autowired
    private AuthService authService;

    /**
     * 获取订单列表（管理员功能）
     */
    @GetMapping
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> getOrders(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(defaultValue = "createdAt") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir,
            @RequestParam(required = false) String orderNumber,
            @RequestParam(required = false) String username,
            @RequestParam(required = false) Order.OrderStatus status,
            @RequestParam(required = false) Order.PaymentStatus paymentStatus,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startDate,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endDate) {
        try {
            Sort sort = sortDir.equalsIgnoreCase("desc") ? Sort.by(sortBy).descending() : Sort.by(sortBy).ascending();
            Pageable pageable = PageRequest.of(page, size, sort);

            Page<Order> orders = orderService.getOrdersWithFilters(
                    null, // userId - 管理员查看所有订单
                    status,
                    paymentStatus,
                    startDate,
                    endDate,
                    orderNumber,
                    username,
                    pageable);

            Map<String, Object> response = new HashMap<>();
            response.put("orders", orders.getContent());
            response.put("currentPage", orders.getNumber());
            response.put("totalItems", orders.getTotalElements());
            response.put("totalPages", orders.getTotalPages());
            response.put("hasNext", orders.hasNext());
            response.put("hasPrevious", orders.hasPrevious());

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("获取订单列表失败: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(Map.of("message", "获取订单列表失败: " + e.getMessage()));
        }
    }

    /**
     * 获取当前用户的订单列表
     */
    @GetMapping("/my")
    public ResponseEntity<?> getMyOrders(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "createdAt") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir,
            @RequestParam(required = false) Order.OrderStatus status) {
        try {
            Long userId = authService.getCurrentUser().getId();
            Sort sort = sortDir.equalsIgnoreCase("desc") ? Sort.by(sortBy).descending() : Sort.by(sortBy).ascending();
            Pageable pageable = PageRequest.of(page, size, sort);

            Page<Order> orders;
            if (status != null) {
                orders = orderService.getUserOrdersByStatus(userId, status, pageable);
            } else {
                orders = orderService.getUserOrders(userId, pageable);
            }

            Map<String, Object> response = new HashMap<>();
            response.put("orders", orders.getContent());
            response.put("currentPage", orders.getNumber());
            response.put("totalItems", orders.getTotalElements());
            response.put("totalPages", orders.getTotalPages());
            response.put("hasNext", orders.hasNext());
            response.put("hasPrevious", orders.hasPrevious());

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("获取用户订单列表失败: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(Map.of("message", "获取用户订单列表失败: " + e.getMessage()));
        }
    }

    /**
     * 根据ID获取订单详情
     */
    @GetMapping("/{id}")
    public ResponseEntity<?> getOrderById(@PathVariable Long id) {
        try {
            Order order = orderService.getOrderById(id);

            // 检查权限：管理员可以查看所有订单，普通用户只能查看自己的订单
            if (!authService.isAdmin()) {
                Long currentUserId = authService.getCurrentUser().getId();
                if (!order.getUser().getId().equals(currentUserId)) {
                    return ResponseEntity.status(403)
                            .body(Map.of("message", "无权访问此订单"));
                }
            }

            return ResponseEntity.ok(order);
        } catch (Exception e) {
            logger.error("获取订单详情失败: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(Map.of("message", "获取订单详情失败: " + e.getMessage()));
        }
    }

    /**
     * 创建订单
     */
    @PostMapping
    public ResponseEntity<?> createOrder(@RequestBody Map<String, Object> orderData) {
        try {
            Long userId = authService.getCurrentUser().getId();
            Order order = orderService.createOrder(userId, orderData);
            return ResponseEntity.ok(order);
        } catch (Exception e) {
            logger.error("创建订单失败: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(Map.of("message", "创建订单失败: " + e.getMessage()));
        }
    }

    /**
     * 更新订单状态（管理员功能）
     */
    @PutMapping("/{id}/status")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> updateOrderStatus(
            @PathVariable Long id,
            @RequestBody Map<String, String> statusData) {
        try {
            String statusStr = statusData.get("status");
            String trackingNumber = statusData.get("trackingNumber");

            Order.OrderStatus status = Order.OrderStatus.valueOf(statusStr);
            Order updatedOrder = orderService.updateOrderStatus(id, status, trackingNumber);

            return ResponseEntity.ok(updatedOrder);
        } catch (Exception e) {
            logger.error("更新订单状态失败: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(Map.of("message", "更新订单状态失败: " + e.getMessage()));
        }
    }

    /**
     * 取消订单
     */
    @PutMapping("/{id}/cancel")
    public ResponseEntity<?> cancelOrder(@PathVariable Long id) {
        try {
            // 检查权限：管理员可以取消所有订单，普通用户只能取消自己的订单
            if (!authService.isAdmin()) {
                Order order = orderService.getOrderById(id);
                Long currentUserId = authService.getCurrentUser().getId();
                if (!order.getUser().getId().equals(currentUserId)) {
                    return ResponseEntity.status(403)
                            .body(Map.of("message", "无权取消此订单"));
                }
            }

            Order cancelledOrder = orderService.cancelOrder(id);
            return ResponseEntity.ok(cancelledOrder);
        } catch (Exception e) {
            logger.error("取消订单失败: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(Map.of("message", "取消订单失败: " + e.getMessage()));
        }
    }

    /**
     * 模拟支付
     */
    @PostMapping("/{id}/pay")
    public ResponseEntity<?> simulatePayment(@PathVariable Long id, @RequestBody Map<String, String> paymentData) {
        try {
            // 检查权限：普通用户只能支付自己的订单
            if (!authService.isAdmin()) {
                Order order = orderService.getOrderById(id);
                Long currentUserId = authService.getCurrentUser().getId();
                if (!order.getUser().getId().equals(currentUserId)) {
                    return ResponseEntity.status(403)
                            .body(Map.of("message", "无权支付此订单"));
                }
            }

            String paymentMethodStr = paymentData.get("paymentMethod");
            Order.PaymentMethod paymentMethod = Order.PaymentMethod.valueOf(paymentMethodStr);

            Order paidOrder = orderService.simulatePayment(id, paymentMethod);
            return ResponseEntity.ok(paidOrder);
        } catch (Exception e) {
            logger.error("模拟支付失败: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(Map.of("message", "支付失败: " + e.getMessage()));
        }
    }

    /**
     * 删除订单（管理员功能）
     */
    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> deleteOrder(@PathVariable Long id) {
        try {
            orderService.deleteOrder(id);
            return ResponseEntity.ok(Map.of("message", "删除订单成功"));
        } catch (Exception e) {
            logger.error("删除订单失败: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(Map.of("message", "删除订单失败: " + e.getMessage()));
        }
    }

    /**
     * 发货（管理员功能）
     */
    @PutMapping("/{id}/ship")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> shipOrder(@PathVariable Long id,
            @RequestBody(required = false) Map<String, String> request) {
        try {
            String trackingNumber = request != null ? request.get("trackingNumber") : null;
            Order order = orderService.shipOrder(id, trackingNumber);
            return ResponseEntity.ok(order);
        } catch (Exception e) {
            logger.error("发货失败: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(Map.of("message", "发货失败: " + e.getMessage()));
        }
    }

    /**
     * 确认收货（用户功能）
     */
    @PutMapping("/{id}/confirm")
    public ResponseEntity<?> confirmOrder(@PathVariable Long id) {
        try {
            // 检查权限：普通用户只能确认自己的订单
            if (!authService.isAdmin()) {
                Order order = orderService.getOrderById(id);
                Long currentUserId = authService.getCurrentUser().getId();
                if (!order.getUser().getId().equals(currentUserId)) {
                    return ResponseEntity.status(403)
                            .body(Map.of("message", "无权确认此订单"));
                }
            }

            Order confirmedOrder = orderService.confirmOrder(id);
            return ResponseEntity.ok(confirmedOrder);
        } catch (Exception e) {
            logger.error("确认收货失败: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(Map.of("message", "确认收货失败: " + e.getMessage()));
        }
    }

    /**
     * 获取订单统计信息（管理员功能）
     */
    @GetMapping("/stats")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> getOrderStats() {
        try {
            Map<String, Object> stats = orderService.getOrderStats();
            return ResponseEntity.ok(stats);
        } catch (Exception e) {
            logger.error("获取订单统计失败: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(Map.of("message", "获取订单统计失败: " + e.getMessage()));
        }
    }
}
