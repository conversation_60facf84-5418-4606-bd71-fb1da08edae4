<template>
  <div class="home">
    <div class="container">
      <!-- 轮播图区域 -->
      <section class="hero-section">
        <el-carousel height="400px" indicator-position="outside">
          <el-carousel-item v-for="item in banners" :key="item.id">
            <div class="carousel-item" :style="{ backgroundImage: `url(${item.image})` }">
              <div class="carousel-content">
                <h2>{{ item.title }}</h2>
                <p>{{ item.description }}</p>
                <el-button type="primary" size="large" @click="$router.push('/books')">
                  立即购买
                </el-button>
              </div>
            </div>
          </el-carousel-item>
        </el-carousel>
      </section>

      <!-- 最新图书 -->
      <section class="featured-section">
        <div class="section-header">
          <h2 class="section-title">最新上架</h2>
          <el-button link @click="$router.push('/books?featured=true')">
            查看更多 <el-icon>
              <ArrowRight />
            </el-icon>
          </el-button>
        </div>
        <div class="books-grid" v-loading="latestLoading">
          <BookCard v-for="book in latestBooks" :key="book.id" :book="book" @add-to-cart="handleAddToCart" />
        </div>
      </section>

      <!-- 热销图书 -->
      <section class="hot-section">
        <div class="section-header">
          <h2 class="section-title">热销图书</h2>
          <el-button link @click="$router.push('/books?sort=sales')">
            查看更多 <el-icon>
              <ArrowRight />
            </el-icon>
          </el-button>
        </div>
        <div class="books-grid" v-loading="hotLoading">
          <BookCard v-for="book in hotBooks" :key="book.id" :book="book" @add-to-cart="handleAddToCart" />
        </div>
      </section>

      <!-- 推荐图书 -->
      <section class="latest-section">
        <div class="section-header">
          <h2 class="section-title">推荐图书</h2>
          <el-button link @click="$router.push('/books?sort=latest')">
            查看更多 <el-icon>
              <ArrowRight />
            </el-icon>
          </el-button>
        </div>
        <div class="books-grid" v-loading="featuredLoading">
          <BookCard v-for="book in featuredBooks" :key="book.id" :book="book" @add-to-cart="handleAddToCart" />
        </div>
      </section>

      <!-- 分类导航 -->
      <section class="categories-section">
        <div class="section-header">
          <h2 class="section-title">图书分类</h2>
        </div>
        <div class="categories-grid">
          <div v-for="category in categories" :key="category" class="category-item"
            @click="$router.push(`/books?category=${category}`)">
            <el-icon size="32">
              <Reading />
            </el-icon>
            <span>{{ category }}</span>
          </div>
        </div>
      </section>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref,onMounted } from 'vue'
  import { useRouter } from 'vue-router'
  import { useCartStore } from '@/stores/cart'
  import { bookAPI } from '@/api/'
  import { ElMessage } from 'element-plus'
  import BookCard from '@/components/BookCard.vue'
  import type { Book, Banner, BooksResponse } from '@/types'
  import books2 from '@/assets/images/books2.png'
  const router = useRouter()
  const cartStore = useCartStore()

  // 响应式数据
  const featuredBooks = ref<Book[]>([])
  const hotBooks = ref<Book[]>([])
  const latestBooks = ref<Book[]>([])
  const categories = ref<string[]>([])
  const featuredLoading = ref(false)
  const hotLoading = ref(false)
  const latestLoading = ref(false)

  // 轮播图数据
  const banners = ref<Banner[]>([
    {
      id: 1,
      title: '发现好书，享受阅读',
      description: '精选优质图书，为您提供最佳的阅读体验',
      image: 'https://images.unsplash.com/photo-1481627834876-b7833e8f5570?w=1200&h=400&fit=crop'
    },
    {
      id: 2,
      title: '知识改变命运',
      description: '在书籍的海洋中寻找属于你的宝藏',
      image: books2
    },
    {
      id: 3,
      title: '新书上架',
      description: '最新最热门的图书，第一时间为您呈现',
      image: 'https://images.unsplash.com/photo-1524995997946-a1c2e315a42f?w=1200&h=400&fit=crop'
    }
  ])
  // 获取最新图书
  const fetchLatestBooks = async () => {
    try {
      latestLoading.value = true
      const response = await bookAPI.getBooks({ sortBy: 'createdAt', sortDir: 'desc', size: 8 })
      latestBooks.value = (response as any)?.books || response || []
    } catch (error) {
      console.error('获取最新图书失败:', error)
    } finally {
      latestLoading.value = false
    }
  }

  // 获取热销图书
  const fetchHotBooks = async () => {
    try {
      hotLoading.value = true
      const response = await bookAPI.getBooks({ sortBy: 'salesCount', sortDir: 'desc', size: 8 })
      hotBooks.value = (response as any)?.books || response || []
    } catch (error) {
      console.error('获取热销图书失败:', error)
    } finally {
      hotLoading.value = false
    }
  }

  // 获取推荐图书
  const fetchFeaturedBooks = async () => {
    try {
      featuredLoading.value = true
      const response = await bookAPI.getBooks({ featured: true, size: 8 })
      featuredBooks.value = (response as any)?.books || response || []
    } catch (error) {
      console.error('获取推荐图书失败:', error)
    } finally {
      featuredLoading.value = false
    }
  }


  // 获取分类
  const fetchCategories = async () => {
    try {
      const response = await bookAPI.getCategories()
      categories.value = (response as any) || []
    } catch (error) {
      console.error('获取分类失败:', error)
    }
  }

  // 添加到购物车
  const handleAddToCart = async (book: Book) => {
    try {
      await cartStore.addItem(book, 1)
      ElMessage.success('已添加到购物车')
    } catch (error) {
      ElMessage.error('添加到购物车失败')
    }
  }

  // 页面初始化
  onMounted(() => {
    fetchFeaturedBooks()
    fetchHotBooks()
    fetchLatestBooks()
    fetchCategories()
  })
</script>

<style scoped>
  .home {
    min-height: 100vh;
  }

  .hero-section {
    margin-bottom: 60px;
  }

  .carousel-item {
    height: 400px;
    background-size: cover;
    background-position: center;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
  }

  .carousel-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.4);
  }

  .carousel-content {
    text-align: center;
    color: white;
    z-index: 1;
    position: relative;
  }

  .carousel-content h2 {
    font-size: 48px;
    font-weight: 600;
    margin-bottom: 16px;
  }

  .carousel-content p {
    font-size: 18px;
    margin-bottom: 32px;
    opacity: 0.9;
  }

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 32px;
  }

  .section-title {
    font-size: 28px;
    font-weight: 600;
    color: #303133;
  }

  .books-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 24px;
    margin-bottom: 60px;
  }

  .categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 20px;
  }

  .category-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 24px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    transition: all 0.3s;
  }

  .category-item:hover {
    transform: translateY(-20px);
    box-shadow: 0 8px 24px rgba(7, 0, 0, 0.521);
  }

  .category-item .el-icon {
    color: #409EFF;
    margin-bottom: 12px;
  }

  .category-item span {
    font-size: 16px;
    font-weight: 500;
    color: #303133;
  }

  @media (max-width: 768px) {
    .carousel-content h2 {
      font-size: 32px;
    }

    .carousel-content p {
      font-size: 16px;
    }

    .books-grid {
      grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
      gap: 16px;
    }

    .categories-grid {
      grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
      gap: 16px;
    }
  }
</style>
