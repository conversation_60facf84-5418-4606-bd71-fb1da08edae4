<template>
  <div class="order-management">
    <div class="page-header">
      <h1>订单管理</h1>
    </div>

    <!-- 搜索和筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="订单号">
          <el-input v-model="searchForm.orderNumber" placeholder="输入订单号" clearable />
        </el-form-item>
        <el-form-item label="用户名">
          <el-input v-model="searchForm.username" placeholder="输入用户名" clearable />
        </el-form-item>
        <el-form-item label="订单状态" class="OrderStatus">
          <el-select v-model="searchForm.status" placeholder="选择状态" clearable>
            <el-option label="全部" value="" />
            <el-option label="待付款" value="PENDING" />
            <el-option label="已付款" value="PAID" />
            <el-option label="已发货" value="SHIPPED" />
            <el-option label="已完成" value="DELIVERED" />
            <el-option label="已取消" value="CANCELLED" />
          </el-select>
        </el-form-item>
        <el-form-item value="时间范围">
          <el-date-picker v-model="searchForm.dateRange" type="daterange" range-separator="至" start-placeholder="开始日期"
            end-placeholder="结束日期" format="YYYY-MM-DD" value-format="YYYY-MM-DD" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 订单列表 -->
    <el-card class="table-card">
      <el-table :data="orders" v-loading="loading" style="width: 100%" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" />

        <el-table-column prop="orderNumber" value="订单号" width="180">
          <template #default="{ row }">
            <el-button link @click="handleViewOrder(row)">
              {{ row.orderNumber }}
            </el-button>
          </template>
        </el-table-column>

        <el-table-column value="用户信息" width="150">
          <template #default="{ row }">
            <div class="user-info">
              <div class="username">{{ row.user?.username }}</div>
              <div class="contact">{{ row.contactName }}</div>
              <div class="phone">{{ row.contactPhone }}</div>
            </div>
          </template>
        </el-table-column>

        <el-table-column value="商品信息" min-width="200">
          <template #default="{ row }">
            <div class="order-items">
              <div v-for="item in (row.orderItems || []).slice(0, 2)" :key="item.id" class="item-summary">
                {{ item.book?.title || item.bookTitle || '未知商品' }} x{{ item.quantity }}
              </div>
              <div v-if="(row.orderItems || []).length > 2" class="more-items">
                等{{ (row.orderItems || []).length }}件商品
              </div>
              <div v-if="!row.orderItems || row.orderItems.length === 0" class="no-items">
                暂无商品信息
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="totalAmount" value="订单金额" width="120">
          <template #default="{ row }">
            <span class="amount">¥{{ row.totalAmount }}</span>
          </template>
        </el-table-column>

        <el-table-column value="订单状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="createdAt" value="下单时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.createdAt) }}
          </template>
        </el-table-column>

        <el-table-column value="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="handleViewOrder(row)">
              查看
            </el-button>
            <el-button v-if="row.status === 'PAID'" size="small" type="primary" @click="handleShipOrder(row.id)">
              发货
            </el-button>
            <el-button v-if="row.status === 'PENDING'" size="small" type="danger" @click="handleCancelOrder(row.id)">
              取消
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 批量操作 -->
      <div class="batch-actions" v-if="selectedOrders.length > 0">
        <span>已选择 {{ selectedOrders.length }} 项</span>
        <el-button size="small" @click="handleBatchShip">批量发货</el-button>
        <el-button size="small" type="danger" @click="handleBatchCancel">批量取消</el-button>
        <el-button size="small" @click="handleExportOrders">导出订单</el-button>
      </div>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[10, 20, 50, 100]"
          :total="total" layout="total, sizes, prev, pager, next, jumper" @size-change="handleSizeChange"
          @current-change="handlePageChange" />
      </div>
    </el-card>

    <!-- 订单详情对话框 -->
    <el-dialog v-model="orderDetailVisible" title="订单详情" width="900px">
      <div v-if="selectedOrder" class="order-detail">
        <!-- 订单基本信息 -->
        <div class="detail-section">
          <h3>订单信息</h3>
          <el-descriptions :column="3" border>
            <el-descriptions-item value="订单号">
              {{ selectedOrder.orderNumber }}
            </el-descriptions-item>
            <el-descriptions-item value="订单状态">
              <el-tag :type="getStatusType(selectedOrder.status)">
                {{ getStatusText(selectedOrder.status) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item value="下单时间">
              {{ formatDate(selectedOrder.createdAt) }}
            </el-descriptions-item>
            <el-descriptions-item value="用户名">
              {{ selectedOrder.user?.username }}
            </el-descriptions-item>
            <el-descriptions-item value="联系人">
              {{ selectedOrder.contactName }}
            </el-descriptions-item>
            <el-descriptions-item value="联系电话">
              {{ selectedOrder.contactPhone }}
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- 商品信息 -->
        <div class="detail-section">
          <h3>商品信息</h3>
          <el-table :data="selectedOrder.orderItems" style="width: 100%">
            <el-table-column value="商品" width="300">
              <template #default="{ row }">
                <div class="product-info">
                  <img :src="row.book.coverUrl || defaultCover" :alt="row.book.title" class="product-image" />
                  <div>
                    <div class="product-title">{{ row.book.title }}</div>
                    <div class="product-author">{{ row.book.author }}</div>
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="price" value="单价" width="100">
              <template #default="{ row }">
                ¥{{ row.price }}
              </template>
            </el-table-column>
            <el-table-column prop="quantity" value="数量" width="80" />
            <el-table-column value="小计">
              <template #default="{ row }">
                ¥{{ (row.price * row.quantity).toFixed(2) }}
              </template>
            </el-table-column>
          </el-table>

          <div class="order-summary">
            <div class="summary-item">
              <span class="label">商品总额：</span>
              <span class="value">¥{{ selectedOrder.totalAmount }}</span>
            </div>
          </div>
        </div>

        <!-- 收货信息 -->
        <div class="detail-section">
          <h3>收货信息</h3>
          <el-descriptions :column="1" border>
            <el-descriptions-item value="收货地址">
              {{ selectedOrder.shippingAddress }}
            </el-descriptions-item>
            <el-descriptions-item value="收货人">
              {{ selectedOrder.contactName }}
            </el-descriptions-item>
            <el-descriptions-item value="联系电话">
              {{ selectedOrder.contactPhone }}
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- 操作按钮 -->
        <div class="detail-actions">
          <el-button v-if="selectedOrder.status === 'PAID'" type="primary" @click="handleShipOrder(selectedOrder.id)">
            发货
          </el-button>
          <el-button v-if="selectedOrder.status === 'PENDING'" type="danger"
            @click="handleCancelOrder(selectedOrder.id)">
            取消订单
          </el-button>
          <el-button @click="handlePrintOrder">打印订单</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted } from 'vue'
  import { orderAPI } from '@/api'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import type { Order } from '@/types'

  // 响应式数据
  const orders = ref<Order[]>([])
  const loading = ref(false)
  const currentPage = ref(1)
  const pageSize = ref(20)
  const total = ref(0)
  const selectedOrders = ref<Order[]>([])
  const orderDetailVisible = ref(false)
  const selectedOrder = ref<Order | null>(null)
  const defaultCover = ref('https://images.unsplash.com/photo-1543002588-bfa74002ed7e?w=60&h=80&fit=crop')

  // 搜索表单
  const searchForm = ref({
    orderNumber: '',
    username: '',
    status: '',
    dateRange: null as any
  })

  // 获取订单列表
  const fetchOrders = async () => {
    try {
      loading.value = true

      const params: any = {
        page: currentPage.value - 1,
        size: pageSize.value
      }

      if (searchForm.value.orderNumber) {
        params.orderNumber = searchForm.value.orderNumber
      }

      if (searchForm.value.username) {
        params.username = searchForm.value.username
      }

      if (searchForm.value.status) {
        params.status = searchForm.value.status
      }

      if (searchForm.value.dateRange && searchForm.value.dateRange.length === 2) {
        params.startDate = searchForm.value.dateRange[0]
        params.endDate = searchForm.value.dateRange[1]
      }

      console.log('===调试：请求参数', params)
      const response = await orderAPI.getAllOrders(params)
      console.log('===调试：API响应', response)

      orders.value = (response as any)?.orders || response || []
      total.value = (response as any)?.totalItems || orders.value.length

      console.log('===调试：处理后的订单数据', orders.value)
      console.log('===调试：总数', total.value)

    } catch (error) {
      console.error('===获取订单列表失败:', error)
      ElMessage.error('获取订单列表失败')
    } finally {
      loading.value = false
    }
  }

  // 搜索处理
  const handleSearch = () => {
    currentPage.value = 1
    fetchOrders()
  }

  const handleReset = () => {
    searchForm.value = {
      orderNumber: '',
      username: '',
      status: '',
      dateRange: null
    }
    handleSearch()
  }

  // 分页处理
  const handlePageChange = (page: number) => {
    currentPage.value = page
    fetchOrders()
  }

  const handleSizeChange = (size: number) => {
    pageSize.value = size
    currentPage.value = 1
    fetchOrders()
  }

  // 选择处理
  const handleSelectionChange = (selection: Order[]) => {
    selectedOrders.value = selection
  }

  // 订单操作
  const handleViewOrder = (order: Order) => {
    selectedOrder.value = order
    orderDetailVisible.value = true
  }

  const handleShipOrder = async (orderId: number) => {
    try {
      const { value: trackingNumber } = await ElMessageBox.prompt('请输入物流单号（可选）', '确认发货', {
        confirmButtonText: '确定发货',
        cancelButtonText: '取消',
        inputPlaceholder: '物流单号',
        inputType: 'text'
      })

      await orderAPI.shipOrder(orderId, trackingNumber || undefined)
      ElMessage.success('发货成功')
      await fetchOrders()

      if (orderDetailVisible.value) {
        orderDetailVisible.value = false
      }

    } catch (error) {
      if (error !== 'cancel') {
        console.error('发货失败:', error)
        ElMessage.error('发货失败')
      }
    }
  }

  const handleCancelOrder = async (orderId: number) => {
    try {
      await ElMessageBox.confirm('确定要取消这个订单吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })

      await orderAPI.cancelOrder(orderId)
      ElMessage.success('订单已取消')
      await fetchOrders()

      if (orderDetailVisible.value) {
        orderDetailVisible.value = false
      }

    } catch (error) {
      if (error !== 'cancel') {
        ElMessage.error('取消订单失败')
      }
    }
  }

  // 批量操作
  const handleBatchShip = async () => {
    const pendingOrders = selectedOrders.value.filter(order => order.status === 'PAID')

    if (pendingOrders.length === 0) {
      ElMessage.warning('请选择已付款的订单')
      return
    }

    try {
      await ElMessageBox.confirm(`确定要批量发货 ${pendingOrders.length} 个订单吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      })

      ElMessage.info('批量发货功能开发中...')

    } catch (error) {
      // 用户取消操作
    }
  }

  const handleBatchCancel = async () => {
    const pendingOrders = selectedOrders.value.filter(order => order.status === 'PENDING')

    if (pendingOrders.length === 0) {
      ElMessage.warning('请选择待付款的订单')
      return
    }

    try {
      await ElMessageBox.confirm(`确定要批量取消 ${pendingOrders.length} 个订单吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })

      ElMessage.info('批量取消功能开发中...')

    } catch (error) {
      // 用户取消操作
    }
  }

  const handleExportOrders = () => {
    ElMessage.info('导出订单功能开发中...')
  }

  const handlePrintOrder = () => {
    ElMessage.info('打印订单功能开发中...')
  }

  // 工具函数
  const getStatusType = (status: string) => {
    const statusMap: Record<string, string> = {
      PENDING: 'warning',
      PAID: 'info',
      SHIPPED: 'primary',
      DELIVERED: 'success',
      CANCELLED: 'danger'
    }
    return statusMap[status] || 'info'
  }

  const getStatusText = (status: string) => {
    const statusMap: Record<string, string> = {
      PENDING: '待付款',
      PAID: '已付款',
      SHIPPED: '已发货',
      DELIVERED: '已完成',
      CANCELLED: '已取消'
    }
    return statusMap[status] || status
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN')
  }

  // 页面初始化
  onMounted(() => {
    fetchOrders()
  })
</script>

<style scoped>
  .order-management {
    max-width: 1400px;
    margin: 0 auto;
  }

  .page-header {
    margin-bottom: 20px;
  }

  .page-header h1 {
    font-size: 24px;
    font-weight: 600;
    color: #303133;
    margin: 0;
  }

  .search-card {
    margin-bottom: 20px;
  }
  .OrderStatus{
    width: 170px;
  }
  .table-card {
    margin-bottom: 20px;
  }

  .user-info {
    font-size: 14px;
  }

  .username {
    font-weight: 500;
    color: #303133;
    margin-bottom: 2px;
  }

  .contact,
  .phone {
    color: #909399;
    font-size: 12px;
  }

  .order-items {
    font-size: 14px;
  }

  .item-summary {
    color: #606266;
    margin-bottom: 2px;
    line-height: 1.4;
  }

  .more-items {
    color: #909399;
    font-size: 12px;
  }

  .amount {
    font-weight: 600;
    color: #e6a23c;
  }

  .batch-actions {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px 0;
    border-top: 1px solid #f0f0f0;
    margin-top: 16px;
  }

  .pagination {
    display: flex;
    justify-content: center;
    padding-top: 20px;
    border-top: 1px solid #f0f0f0;
  }

  .order-detail {
    max-height: 600px;
    overflow-y: auto;
  }

  .detail-section {
    margin-bottom: 24px;
  }

  .detail-section h3 {
    margin: 0 0 16px 0;
    font-size: 16px;
    font-weight: 600;
    color: #303133;
  }

  .product-info {
    display: flex;
    gap: 12px;
    align-items: center;
  }

  .product-image {
    width: 40px;
    height: 50px;
    object-fit: cover;
    border-radius: 4px;
  }

  .product-title {
    font-weight: 500;
    color: #303133;
    margin-bottom: 4px;
  }

  .product-author {
    font-size: 12px;
    color: #909399;
  }

  .order-summary {
    text-align: right;
    padding-top: 16px;
    border-top: 1px solid #f0f0f0;
    margin-top: 16px;
  }

  .summary-item {
    margin-bottom: 8px;
  }

  .summary-item .label {
    color: #606266;
    margin-right: 8px;
  }

  .summary-item .value {
    font-weight: 600;
    color: #e6a23c;
    font-size: 16px;
  }

  .detail-actions {
    display: flex;
    justify-content: center;
    gap: 12px;
    padding-top: 24px;
    border-top: 1px solid #f0f0f0;
  }

  @media (max-width: 768px) {
    .batch-actions {
      flex-wrap: wrap;
      gap: 8px;
    }

    .detail-actions {
      flex-wrap: wrap;
      justify-content: center;
    }
  }
</style>
