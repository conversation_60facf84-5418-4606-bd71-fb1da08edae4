package com.booksxm.service;

import com.booksxm.entity.Book;
import com.booksxm.entity.CartItem;
import com.booksxm.entity.User;
import com.booksxm.repository.BookRepository;
import com.booksxm.repository.CartItemRepository;
import com.booksxm.repository.UserRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

@Service
@Transactional
public class CartService {

    private static final Logger logger = LoggerFactory.getLogger(CartService.class);

    @Autowired
    private CartItemRepository cartItemRepository;

    @Autowired
    private BookRepository bookRepository;

    @Autowired
    private UserRepository userRepository;

    /**
     * 获取用户购物车
     */
    @Transactional(readOnly = true)
    public List<CartItem> getUserCart(Long userId) {
        // 使用JOIN FETCH避免懒加载问题
        return cartItemRepository.findByUserIdWithBook(userId);
    }

    /**
     * 添加商品到购物车
     */
    public CartItem addToCart(Long userId, Long bookId, Integer quantity) {
        try {
            User user = userRepository.findById(userId)
                    .orElseThrow(() -> new RuntimeException("用户不存在"));

            Book book = bookRepository.findById(bookId)
                    .orElseThrow(() -> new RuntimeException("图书不存在"));

            if (!book.isAvailable()) {
                throw new RuntimeException("图书不可购买");
            }

            if (book.getStock() < quantity) {
                throw new RuntimeException("库存不足");
            }

            // 检查是否已存在该商品
            Optional<CartItem> existingItem = cartItemRepository.findByUserAndBook(user, book);

            CartItem cartItem;
            if (existingItem.isPresent()) {
                // 更新数量
                cartItem = existingItem.get();
                int newQuantity = cartItem.getQuantity() + quantity;

                if (book.getStock() < newQuantity) {
                    throw new RuntimeException("库存不足");
                }

                cartItem.setQuantity(newQuantity);
            } else {
                // 创建新的购物车项
                cartItem = new CartItem(user, book, quantity);
            }

            CartItem savedItem = cartItemRepository.save(cartItem);
            logger.info("用户 {} 添加商品到购物车: {} x {}", user.getUsername(), book.getTitle(), quantity);
            return savedItem;

        } catch (Exception e) {
            logger.error("添加到购物车失败: {}", e.getMessage());
            throw new RuntimeException("添加到购物车失败: " + e.getMessage());
        }
    }

    /**
     * 更新购物车商品数量
     */
    public CartItem updateCartItem(Long userId, Long cartItemId, Integer quantity) {
        try {
            // 使用JOIN FETCH避免懒加载问题
            CartItem cartItem = cartItemRepository.findByIdWithBook(cartItemId)
                    .orElseThrow(() -> new RuntimeException("购物车项不存在"));

            if (!cartItem.getUser().getId().equals(userId)) {
                throw new RuntimeException("无权限操作此购物车项");
            }

            if (quantity <= 0) {
                cartItemRepository.delete(cartItem);
                logger.info("删除购物车项: {}", cartItem.getId());
                return null;
            }

            Book book = cartItem.getBook();
            if (book.getStock() < quantity) {
                throw new RuntimeException("库存不足");
            }

            cartItem.setQuantity(quantity);
            CartItem updatedItem = cartItemRepository.save(cartItem);

            logger.info("更新购物车项数量: {} -> {}", cartItem.getId(), quantity);
            return updatedItem;

        } catch (Exception e) {
            logger.error("更新购物车失败: {}", e.getMessage());
            throw new RuntimeException("更新购物车失败: " + e.getMessage());
        }
    }

    /**
     * 删除购物车商品
     */
    public void removeFromCart(Long userId, Long cartItemId) {
        try {
            CartItem cartItem = cartItemRepository.findById(cartItemId)
                    .orElseThrow(() -> new RuntimeException("购物车项不存在"));

            if (!cartItem.getUser().getId().equals(userId)) {
                throw new RuntimeException("无权限操作此购物车项");
            }

            cartItemRepository.delete(cartItem);
            logger.info("删除购物车项: {}", cartItem.getId());

        } catch (Exception e) {
            logger.error("删除购物车项失败: {}", e.getMessage());
            throw new RuntimeException("删除购物车项失败: " + e.getMessage());
        }
    }

    /**
     * 清空购物车
     */
    public void clearCart(Long userId) {
        try {
            User user = userRepository.findById(userId)
                    .orElseThrow(() -> new RuntimeException("用户不存在"));

            cartItemRepository.deleteByUser(user);
            logger.info("清空用户购物车: {}", user.getUsername());

        } catch (Exception e) {
            logger.error("清空购物车失败: {}", e.getMessage());
            throw new RuntimeException("清空购物车失败: " + e.getMessage());
        }
    }

    /**
     * 更新购物车项选中状态
     */
    public CartItem updateSelection(Long userId, Long cartItemId, Boolean selected) {
        try {
            CartItem cartItem = cartItemRepository.findById(cartItemId)
                    .orElseThrow(() -> new RuntimeException("购物车项不存在"));

            if (!cartItem.getUser().getId().equals(userId)) {
                throw new RuntimeException("无权限操作此购物车项");
            }

            cartItem.setIsSelected(selected);
            CartItem updatedItem = cartItemRepository.save(cartItem);

            logger.info("更新购物车项选中状态: {} -> {}", cartItem.getId(), selected);
            return updatedItem;

        } catch (Exception e) {
            logger.error("更新选中状态失败: {}", e.getMessage());
            throw new RuntimeException("更新选中状态失败: " + e.getMessage());
        }
    }

    /**
     * 全选/取消全选
     */
    public void updateAllSelection(Long userId, Boolean selected) {
        try {
            User user = userRepository.findById(userId)
                    .orElseThrow(() -> new RuntimeException("用户不存在"));

            cartItemRepository.updateSelectionByUser(user, selected);
            logger.info("更新用户所有购物车项选中状态: {} -> {}", user.getUsername(), selected);

        } catch (Exception e) {
            logger.error("更新全选状态失败: {}", e.getMessage());
            throw new RuntimeException("更新全选状态失败: " + e.getMessage());
        }
    }

    /**
     * 获取购物车统计信息
     */
    @Transactional(readOnly = true)
    public CartSummary getCartSummary(Long userId) {
        try {
            List<CartItem> cartItems = cartItemRepository.findByUserIdWithBook(userId);
            return calculateCartSummary(cartItems);
        } catch (Exception e) {
            logger.error("获取购物车统计失败: {}", e.getMessage());
            throw new RuntimeException("获取购物车统计失败: " + e.getMessage());
        }
    }

    /**
     * 基于已有的购物车项目计算统计信息（避免重复查询）
     */
    public CartSummary calculateCartSummary(List<CartItem> cartItems) {
        try {
            List<CartItem> selectedItems = cartItems.stream()
                    .filter(CartItem::getIsSelected)
                    .toList();

            int totalItems = cartItems.stream()
                    .mapToInt(CartItem::getQuantity)
                    .sum();

            int selectedItemCount = selectedItems.stream()
                    .mapToInt(CartItem::getQuantity)
                    .sum();

            BigDecimal totalPrice = selectedItems.stream()
                    .map(item -> item.getBook().getPrice().multiply(BigDecimal.valueOf(item.getQuantity())))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            return new CartSummary(totalItems, selectedItemCount, totalPrice, cartItems.size());
        } catch (Exception e) {
            logger.error("计算购物车统计失败: {}", e.getMessage());
            return new CartSummary(0, 0, BigDecimal.ZERO, 0);
        }
    }

    /**
     * 获取选中的购物车项
     */
    @Transactional(readOnly = true)
    public List<CartItem> getSelectedItems(Long userId) {
        return cartItemRepository.findByUserIdAndIsSelectedWithBook(userId, true);
    }

    /**
     * 购物车统计信息DTO
     */
    public static class CartSummary {
        private int totalItems;
        private int selectedItemCount;
        private BigDecimal totalPrice;
        private int uniqueItems;

        public CartSummary(int totalItems, int selectedItemCount, BigDecimal totalPrice, int uniqueItems) {
            this.totalItems = totalItems;
            this.selectedItemCount = selectedItemCount;
            this.totalPrice = totalPrice;
            this.uniqueItems = uniqueItems;
        }

        // Getter方法
        public int getTotalItems() {
            return totalItems;
        }

        public int getSelectedItemCount() {
            return selectedItemCount;
        }

        public BigDecimal getTotalPrice() {
            return totalPrice;
        }

        public int getUniqueItems() {
            return uniqueItems;
        }
    }
}
