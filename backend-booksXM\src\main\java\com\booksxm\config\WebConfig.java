package com.booksxm.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.nio.file.Path;
import java.nio.file.Paths;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
@Configuration
public class WebConfig implements WebMvcConfigurer {
    // ========= 定义日志对象 ============
    private static final Logger logger = LoggerFactory.getLogger(WebConfig.class);

    @Value("${file.upload-dir:uploads}")
    private String uploadDir;
    // 本地图片存储根路径（根据实际路径修改）
    private static final String LOCAL_BOOK_COVER_PATH = "file:D:/Desktop/books/backend-booksXM/book-covers/";
    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // ===现在可以正常使用logger了
        logger.info("映射规则：/covers/** → {}", LOCAL_BOOK_COVER_PATH);
        // 配置上传文件的静态资源映射
        Path uploadPath = Paths.get(uploadDir).toAbsolutePath().normalize();
        String uploadPathUri = uploadPath.toUri().toString();
        
        registry.addResourceHandler("/api/uploads/**")
                .addResourceLocations(uploadPathUri);

        // ===前端访问路径：http://localhost:8080/covers/xxx.jpg
        // ===映射到本地路径：D:/book-covers/xxx.jpg
        registry.addResourceHandler("/covers/**") // 前端访问的URL前缀
                .addResourceLocations(LOCAL_BOOK_COVER_PATH); // 本地图片文件夹路径

    }
}
