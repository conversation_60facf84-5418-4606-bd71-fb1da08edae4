<template>
  <div class="dashboard">
    <div class="dashboard-header">
      <h1>仪表盘</h1>
      <p>欢迎来到图书商城管理后台</p>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <el-row :gutter="20">
        <el-col :xs="24" :sm="12" :md="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon books">
                <el-icon>
                  <Reading />
                </el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ stats.totalBooks }}</div>
                <div class="stat-label">图书总数</div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :xs="24" :sm="12" :md="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon orders">
                <el-icon>
                  <Document />
                </el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ stats.totalOrders }}</div>
                <div class="stat-label">订单总数</div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :xs="24" :sm="12" :md="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon users">
                <el-icon>
                  <User />
                </el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ stats.totalUsers }}</div>
                <div class="stat-label">用户总数</div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :xs="24" :sm="12" :md="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon revenue">
                <el-icon>
                  <Money />
                </el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">¥{{ stats.totalRevenue }}</div>
                <div class="stat-label">总收入</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 图表和列表 -->
    <el-row :gutter="20" class="dashboard-content">
      <!-- 最近订单 -->
      <el-col :xs="24" :lg="12">
        <el-card header="最近订单">
          <div v-loading="ordersLoading">
            <div v-if="recentOrders.length === 0" class="empty-data">
              <el-empty description="暂无订单" />
            </div>
            <div v-else class="recent-orders">
              <div v-for="order in recentOrders" :key="order.id" class="order-item">
                <div class="order-info">
                  <div class="order-number">{{ order.orderNumber }}</div>
                  <div class="order-user">{{ order.user?.username }}</div>
                </div>
                <div class="order-details">
                  <div class="order-amount">¥{{ order.totalAmount }}</div>
                  <el-tag :type="getStatusType(order.status)" size="small">
                    {{ getStatusText(order.status) }}
                  </el-tag>
                </div>
              </div>
            </div>
            <div class="view-more">
              <el-button link @click="$router.push('/admin/orders')">
                查看全部订单
              </el-button>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 热销图书 -->
      <el-col :xs="24" :lg="12">
        <el-card header="热销图书">
          <div v-loading="booksLoading">
            <div v-if="hotBooks.length === 0" class="empty-data">
              <el-empty description="暂无数据" />
            </div>
            <div v-else class="hot-books">
              <div v-for="(book, index) in hotBooks" :key="book.id" class="book-item">
                <div class="book-rank">{{ index + 1 }}</div>
                <div class="book-cover">
                  <img :src="book.coverUrl || defaultCover" :alt="book.title" />
                </div>
                <div class="book-info">
                  <div class="book-title">{{ book.title }}</div>
                  <div class="book-author">{{ book.author }}</div>
                </div>
                <div class="book-sales">
                  <div class="sales-count">{{ book.salesCount }}本</div>
                  <div class="book-price">¥{{ book.price }}</div>
                </div>
              </div>
            </div>
            <div class="view-more">
              <el-button link @click="$router.push('/admin/books')">
                查看全部图书
              </el-button>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 快捷操作 -->
    <el-card header="快捷操作" class="quick-actions">
      <el-row :gutter="20">
        <el-col :xs="24" :sm="12" :md="6">
          <el-button type="primary" size="large" class="action-button" @click="$router.push('/admin/books')">
            <el-icon>
              <Plus />
            </el-icon>
            添加图书
          </el-button>
        </el-col>
        <el-col :xs="24" :sm="12" :md="6">
          <el-button type="success" size="large" class="action-button" @click="$router.push('/admin/orders')">
            <el-icon>
              <Document />
            </el-icon>
            处理订单
          </el-button>
        </el-col>
        <el-col :xs="24" :sm="12" :md="6">
          <el-button type="info" size="large" class="action-button" @click="$router.push('/admin/users')">
            <el-icon>
              <User />
            </el-icon>
            用户管理
          </el-button>
        </el-col>
        <el-col :xs="24" :sm="12" :md="6">
          <el-button type="warning" size="large" class="action-button" @click="handleExportData">
            <el-icon>
              <Download />
            </el-icon>
            导出数据
          </el-button>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted } from 'vue'
  import { orderAPI, bookAPI } from '@/api'
  import { ElMessage } from 'element-plus'
  import type { Order, Book } from '@/types'

  // 响应式数据
  const stats = ref({
    totalBooks: 0,
    totalOrders: 0,
    totalUsers: 0,
    totalRevenue: 0
  })

  const recentOrders = ref<Order[]>([])
  const hotBooks = ref<Book[]>([])
  const ordersLoading = ref(false)
  const booksLoading = ref(false)
  const defaultCover = ref('https://images.unsplash.com/photo-1543002588-bfa74002ed7e?w=60&h=80&fit=crop')

  // 获取统计数据
  const fetchStats = async () => {
    try {
      // 这里应该调用实际的统计API
      // 暂时使用模拟数据
      stats.value = {
        totalBooks: 1250,
        totalOrders: 3680,
        totalUsers: 892,
        totalRevenue: 125680
      }
    } catch (error) {
      console.error('获取统计数据失败:', error)
    }
  }

  // 获取最近订单
  const fetchRecentOrders = async () => {
    try {
      ordersLoading.value = true
      const response = await orderAPI.getAllOrders({ page: 0, size: 5 })
      recentOrders.value = (response as any)?.orders || response || []
    } catch (error) {
      console.error('获取最近订单失败:', error)
    } finally {
      ordersLoading.value = false
    }
  }

  // 获取热销图书
  const fetchHotBooks = async () => {
    try {
      booksLoading.value = true
      const response = await bookAPI.getBooks({
        sortBy: 'salesCount',
        sortDir: 'desc',
        size: 5
      })
      hotBooks.value = (response as any)?.books || response || []
    } catch (error) {
      console.error('获取热销图书失败:', error)
    } finally {
      booksLoading.value = false
    }
  }

  // 工具函数
  const getStatusType = (status: string) => {
    const statusMap: Record<string, string> = {
      PENDING: 'warning',
      PAID: 'info',
      SHIPPED: 'primary',
      DELIVERED: 'success',
      CANCELLED: 'danger'
    }
    return statusMap[status] || 'info'
  }

  const getStatusText = (status: string) => {
    const statusMap: Record<string, string> = {
      PENDING: '待付款',
      PAID: '已付款',
      SHIPPED: '已发货',
      DELIVERED: '已完成',
      CANCELLED: '已取消'
    }
    return statusMap[status] || status
  }

  // 导出数据
  const handleExportData = () => {
    ElMessage.info('导出功能开发中...')
  }

  // 页面初始化
  onMounted(() => {
    fetchStats()
    fetchRecentOrders()
    fetchHotBooks()
  })
</script>

<style scoped>
  .dashboard {
    max-width: 1200px;
    margin: 0 auto;
  }

  .dashboard-header {
    margin-bottom: 24px;
  }

  .dashboard-header h1 {
    font-size: 28px;
    font-weight: 600;
    color: #303133;
    margin: 0 0 8px 0;
  }

  .dashboard-header p {
    color: #909399;
    margin: 0;
  }

  .stats-cards {
    margin-bottom: 24px;
  }

  .stat-card {
    height: 120px;
  }

  .stat-content {
    display: flex;
    align-items: center;
    height: 100%;
  }

  .stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 16px;
    font-size: 24px;
    color: white;
  }

  .stat-icon.books {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }

  .stat-icon.orders {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  }

  .stat-icon.users {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  }

  .stat-icon.revenue {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  }

  .stat-info {
    flex: 1;
  }

  .stat-number {
    font-size: 24px;
    font-weight: 600;
    color: #303133;
    margin-bottom: 4px;
  }

  .stat-label {
    color: #909399;
    font-size: 14px;
  }

  .dashboard-content {
    margin-bottom: 24px;
  }

  .empty-data {
    padding: 40px 20px;
    text-align: center;
  }

  .recent-orders {
    margin-bottom: 16px;
  }

  .order-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f5f5f5;
  }

  .order-item:last-child {
    border-bottom: none;
  }

  .order-number {
    font-weight: 500;
    color: #303133;
    margin-bottom: 4px;
  }

  .order-user {
    font-size: 14px;
    color: #909399;
  }

  .order-details {
    text-align: right;
  }

  .order-amount {
    font-weight: 600;
    color: #e6a23c;
    margin-bottom: 4px;
  }

  .hot-books {
    margin-bottom: 16px;
  }

  .book-item {
    display: flex;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f5f5f5;
  }

  .book-item:last-child {
    border-bottom: none;
  }

  .book-rank {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: #409EFF;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: 600;
    margin-right: 12px;
  }

  .book-cover {
    width: 40px;
    height: 50px;
    margin-right: 12px;
  }

  .book-cover img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 4px;
  }

  .book-info {
    flex: 1;
    margin-right: 12px;
  }

  .book-title {
    font-weight: 500;
    color: #303133;
    margin-bottom: 4px;
    font-size: 14px;
    line-height: 1.4;
  }

  .book-author {
    font-size: 12px;
    color: #909399;
  }

  .book-sales {
    text-align: right;
  }

  .sales-count {
    font-size: 14px;
    color: #67c23a;
    margin-bottom: 4px;
  }

  .book-price {
    font-weight: 600;
    color: #e6a23c;
    font-size: 14px;
  }

  .view-more {
    text-align: center;
    padding-top: 16px;
    border-top: 1px solid #f5f5f5;
  }

  .quick-actions {
    margin-bottom: 24px;
  }

  .action-button {
    width: 100%;
    height: 60px;
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  @media (max-width: 768px) {
    .dashboard-header h1 {
      font-size: 24px;
    }

    .stat-content {
      flex-direction: column;
      text-align: center;
      gap: 12px;
    }

    .stat-icon {
      margin-right: 0;
    }

    .order-item,
    .book-item {
      flex-direction: column;
      align-items: flex-start;
      gap: 8px;
    }

    .order-details,
    .book-sales {
      text-align: left;
      align-self: stretch;
    }
  }
</style>
