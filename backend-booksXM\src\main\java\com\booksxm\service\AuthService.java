package com.booksxm.service;

import com.booksxm.dto.LoginRequest;
import com.booksxm.dto.LoginResponse;
import com.booksxm.dto.RegisterRequest;
import com.booksxm.entity.User;
import com.booksxm.repository.UserRepository;
import com.booksxm.util.JwtUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Transactional
public class AuthService {

    private static final Logger logger = LoggerFactory.getLogger(AuthService.class);

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Autowired
    private JwtUtil jwtUtil;

    @Autowired
    private AuthenticationManager authenticationManager;

    /**
     * 用户登录
     */
    public LoginResponse login(LoginRequest loginRequest) {
        try {
            // 认证用户
            Authentication authentication = authenticationManager.authenticate(
                    new UsernamePasswordAuthenticationToken(
                            loginRequest.getUsername(),
                            loginRequest.getPassword()));

            SecurityContextHolder.getContext().setAuthentication(authentication);

            // 获取用户信息
            User user = userRepository.findByUsernameOrEmail(loginRequest.getUsername())
                    .orElseThrow(() -> new RuntimeException("用户不存在"));

            // 生成JWT令牌
            String token = jwtUtil.generateToken(
                    user.getUsername(),
                    user.getRole().name(),
                    user.getId());

            logger.info("用户 {} 登录成功", user.getUsername());

            return new LoginResponse(token, user);

        } catch (Exception e) {
            logger.error("用户登录失败: {}", e.getMessage());
            throw new RuntimeException("用户名或密码错误");
        }
    }

    /**
     * 用户注册
     */
    public User register(RegisterRequest registerRequest) {
        try {
            // 检查用户名是否已存在
            if (userRepository.existsByUsername(registerRequest.getUsername())) {
                throw new RuntimeException("用户名已存在");
            }

            // 检查邮箱是否已存在
            if (userRepository.existsByEmail(registerRequest.getEmail())) {
                throw new RuntimeException("邮箱已被注册");
            }

            // 创建新用户
            User user = new User();
            user.setUsername(registerRequest.getUsername());
            user.setEmail(registerRequest.getEmail());
            user.setPassword(passwordEncoder.encode(registerRequest.getPassword()));
            user.setRole(User.Role.USER);
            user.setIsActive(true);
            user.setEmailVerified(false);

            User savedUser = userRepository.save(user);

            logger.info("新用户注册成功: {}", savedUser.getUsername());

            return savedUser;

        } catch (Exception e) {
            logger.error("用户注册失败: {}", e.getMessage());
            throw new RuntimeException("注册失败: " + e.getMessage());
        }
    }

    /**
     * 刷新令牌
     */
    public LoginResponse refreshToken(String token) {
        try {
            if (!jwtUtil.isValidToken(token)) {
                throw new RuntimeException("无效的令牌");
            }

            String username = jwtUtil.getUsernameFromToken(token);
            User user = userRepository.findByUsername(username)
                    .orElseThrow(() -> new RuntimeException("用户不存在"));

            if (!user.getIsActive()) {
                throw new RuntimeException("用户已被禁用");
            }

            String newToken = jwtUtil.refreshToken(token);

            logger.info("用户 {} 刷新令牌成功", username);

            return new LoginResponse(newToken, user);

        } catch (Exception e) {
            logger.error("刷新令牌失败: {}", e.getMessage());
            throw new RuntimeException("刷新令牌失败: " + e.getMessage());
        }
    }

    /**
     * 验证令牌
     */
    public boolean validateToken(String token) {
        try {
            if (!jwtUtil.isValidToken(token)) {
                return false;
            }

            String username = jwtUtil.getUsernameFromToken(token);
            User user = userRepository.findByUsername(username).orElse(null);

            return user != null && user.getIsActive() && jwtUtil.validateToken(token, username);

        } catch (Exception e) {
            logger.error("验证令牌失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 获取当前登录用户
     */
    public User getCurrentUser() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || !authentication.isAuthenticated()) {
            return null;
        }

        String username = authentication.getName();
        return userRepository.findByUsername(username).orElse(null);
    }

    /**
     * 检查用户名是否可用
     */
    public boolean isUsernameAvailable(String username) {
        return !userRepository.existsByUsername(username);
    }

    /**
     * 检查邮箱是否可用
     */
    public boolean isEmailAvailable(String email) {
        return !userRepository.existsByEmail(email);
    }

    /**
     * 检查当前用户是否为管理员
     */
    public boolean isAdmin() {
        User currentUser = getCurrentUser();
        return currentUser != null && currentUser.getRole() == User.Role.ADMIN;
    }
}
