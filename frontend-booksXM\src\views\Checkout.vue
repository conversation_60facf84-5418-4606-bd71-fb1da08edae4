<template>
  <div class="checkout-container">
    <div class="checkout-header">
      <h1>结算页面</h1>
      <el-breadcrumb separator="/">
        <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
        <el-breadcrumb-item :to="{ path: '/cart' }">购物车</el-breadcrumb-item>
        <el-breadcrumb-item>结算</el-breadcrumb-item>
      </el-breadcrumb>
    </div>

    <div class="checkout-content">
      <el-row :gutter="24">
        <!-- 左侧：收货信息 -->
        <el-col :span="16">
          <div class="checkout-section">
            <h2>收货信息</h2>

            <!-- 地址选择器 -->
            <div class="address-selector" v-if="savedAddresses.length > 0">
              <h3>选择收货地址</h3>
              <el-radio-group v-model="selectedAddressId" @change="handleAddressChange">
                <div class="address-list">
                  <el-radio v-for="address in savedAddresses" :key="address.id" :value="address.id"
                    class="address-item">
                    <div class="address-content">
                      <div class="address-header">
                        <span class="receiver-name">{{ address.name }}</span>
                        <span class="receiver-phone">{{ address.phone }}</span>
                        <el-tag v-if="address.isDefault" type="primary" size="small">默认</el-tag>
                      </div>
                      <div class="address-detail">
                        {{ address.province }} {{ address.city }} {{ address.district }} {{ address.detail }}
                      </div>
                    </div>
                  </el-radio>
                  <el-radio value="manual" class="address-item">
                    <div class="address-content">
                      <div class="address-header">
                        <span class="receiver-name">手动输入新地址</span>
                      </div>
                    </div>
                  </el-radio>
                </div>
              </el-radio-group>
            </div>

            <!-- 手动输入表单 -->
            <el-form :model="orderForm" :rules="rules" ref="orderFormRef" label-width="100px"
              v-show="selectedAddressId === 'manual' || savedAddresses.length === 0">
              <el-form-item value="收货人" prop="receiverName">
                <el-input v-model="orderForm.receiverName" placeholder="请输入收货人姓名" />
              </el-form-item>
              <el-form-item value="联系电话" prop="receiverPhone">
                <el-input v-model="orderForm.receiverPhone" placeholder="请输入联系电话" />
              </el-form-item>
              <el-form-item value="收货地址" prop="shippingAddress">
                <el-input v-model="orderForm.shippingAddress" type="textarea" :rows="3" placeholder="请输入详细收货地址" />
              </el-form-item>
              <el-form-item value="备注">
                <el-input v-model="orderForm.notes" type="textarea" :rows="2" placeholder="订单备注（可选）" />
              </el-form-item>
            </el-form>
          </div>

          <div class="checkout-section">
            <h2>支付方式</h2>
            <el-radio-group v-model="orderForm.paymentMethod">
              <el-radio value="ALIPAY">
                <el-icon>
                  <CreditCard />
                </el-icon>
                支付宝
              </el-radio>
              <el-radio value="WECHAT">
                <el-icon>
                  <CreditCard />
                </el-icon>
                微信支付
              </el-radio>
              <el-radio value="BANK_CARD">
                <el-icon>
                  <CreditCard />
                </el-icon>
                银行卡
              </el-radio>
              <el-radio value="CASH_ON_DELIVERY">
                <el-icon>
                  <Money />
                </el-icon>
                货到付款
              </el-radio>
            </el-radio-group>
          </div>
        </el-col>

        <!-- 右侧：订单信息 -->
        <el-col :span="8">
          <div class="order-summary">
            <h2>订单信息</h2>

            <div class="order-items">
              <div v-for="item in selectedItems" :key="item.id" class="order-item">
                <div class="item-image">
                  <img :src="item.book.coverUrl || '/placeholder-book.jpg'" :alt="item.book.title" />
                </div>
                <div class="item-info">
                  <div class="item-title">{{ item.book.title }}</div>
                  <div class="item-author">{{ item.book.author }}</div>
                  <div class="item-price">
                    ¥{{ item.book.price.toFixed(2) }} × {{ item.quantity }}
                  </div>
                </div>
                <div class="item-total">
                  ¥{{ (item.book.price * item.quantity).toFixed(2) }}
                </div>
              </div>
            </div>

            <div class="price-summary">
              <div class="price-row">
                <span>商品总价：</span>
                <span>¥{{ totalPrice.toFixed(2) }}</span>
              </div>
              <div class="price-row">
                <span>运费：</span>
                <span>¥{{ shippingFee.toFixed(2) }}</span>
              </div>
              <div class="price-row total">
                <span>应付总额：</span>
                <span class="total-amount">¥{{ finalAmount.toFixed(2) }}</span>
              </div>
            </div>

            <div class="checkout-actions">
              <el-button @click="goBack" size="large">返回购物车</el-button>
              <el-button type="primary" size="large" @click="submitOrder" :loading="submitting" class="submit-btn">
                {{ submitting ? '处理中...' : '提交订单并付款' }}
              </el-button>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, onMounted } from 'vue'
  import { useRouter } from 'vue-router'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import { CreditCard, Money } from '@element-plus/icons-vue'
  import { useCartStore } from '@/stores/cart'
  import { orderApi, type CreateOrderRequest } from '@/api/order'
  import { userAPI, orderAPI } from '@/api/index'

  const router = useRouter()
  const cartStore = useCartStore()

  // 地址类型定义
  interface Address {
    id: number
    name: string
    phone: string
    province: string
    city: string
    district: string
    detail: string
    isDefault: boolean
  }

  // 表单引用
  const orderFormRef = ref()

  // 用户地址
  const savedAddresses = ref<Address[]>([])
  const selectedAddressId = ref<number | string>('manual')

  // 订单表单类型
  type OrderForm = Pick<CreateOrderRequest, 'receiverName' | 'receiverPhone' | 'shippingAddress' | 'notes' | 'paymentMethod'>

  // 订单表单
  const orderForm = ref<OrderForm>({
    receiverName: '',
    receiverPhone: '',
    shippingAddress: '',
    notes: '',
    paymentMethod: 'ALIPAY'
  })

  // 获取用户地址列表
  const fetchAddresses = async () => {
    try {
      const response = await userAPI.getAddresses()
      savedAddresses.value = response.data || response || []

      // 如果有默认地址，自动选中
      const defaultAddress = savedAddresses.value.find(addr => addr.isDefault)
      if (defaultAddress) {
        selectedAddressId.value = defaultAddress.id
        handleAddressChange(defaultAddress.id)
      } else if (savedAddresses.value.length > 0) {
        selectedAddressId.value = savedAddresses.value[0].id
        handleAddressChange(savedAddresses.value[0].id)
      }
    } catch (error) {
      console.error('获取地址列表失败:', error)
    }
  }

  // 处理地址选择变化
  const handleAddressChange = (addressId: number | string) => {
    if (addressId === 'manual') {
      // 手动输入模式，清空表单
      orderForm.value.receiverName = ''
      orderForm.value.receiverPhone = ''
      orderForm.value.shippingAddress = ''
      return
    }

    // 查找选中的地址
    const selectedAddress = savedAddresses.value.find(addr => addr.id === addressId)
    if (selectedAddress) {
      // 填充表单
      orderForm.value.receiverName = selectedAddress.name
      orderForm.value.receiverPhone = selectedAddress.phone
      orderForm.value.shippingAddress = `${selectedAddress.province} ${selectedAddress.city} ${selectedAddress.district} ${selectedAddress.detail}`
    }
  }

  // 表单验证规则
  const rules = {
    receiverName: [
      { required: true, message: '请输入收货人姓名', trigger: 'blur' }
    ],
    receiverPhone: [
      { required: true, message: '请输入联系电话', trigger: 'blur' },
      { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
    ],
    shippingAddress: [
      { required: true, message: '请输入收货地址', trigger: 'blur' }
    ]
  }

  // 提交状态
  const submitting = ref(false)

  // 选中的商品
  const selectedItems = computed(() => {
    return cartStore.items.filter(item => item.selected)
  })

  // 商品总价
  const totalPrice = computed(() => {
    return selectedItems.value.reduce((total, item) => {
      return total + (item.book.price * item.quantity)
    }, 0)
  })

  // 运费（满99免运费）
  const shippingFee = computed(() => {
    return totalPrice.value >= 99 ? 0 : 10
  })

  // 最终金额
  const finalAmount = computed(() => {
    return totalPrice.value + shippingFee.value
  })

  // 返回购物车
  const goBack = () => {
    router.push('/cart')
  }

  // 提交订单
  const submitOrder = async () => {
    if (!orderFormRef.value) return

    try {
      // 验证表单
      await orderFormRef.value.validate()

      if (selectedItems.value.length === 0) {
        ElMessage.error('没有选中的商品')
        return
      }

      // 确认提交并付款
      await ElMessageBox.confirm(
        `确认提交订单并付款？应付金额：¥${finalAmount.value.toFixed(2)}`,
        '确认订单并付款',
        {
          confirmButtonText: '确认付款',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )

      submitting.value = true

      // 准备订单数据
      const orderData: CreateOrderRequest = {
        ...orderForm.value,
        items: selectedItems.value.map(item => ({
          bookId: item.book.id,
          quantity: item.quantity,
          unitPrice: item.book.price
        })),
        totalAmount: totalPrice.value,
        shippingFee: shippingFee.value,
        finalAmount: finalAmount.value
      }

      // 提交订单
      const order = await orderApi.createOrder(orderData)

      // 模拟支付成功，直接将订单状态设置为已付款
      await orderAPI.simulatePayment(order.id, orderForm.value.paymentMethod)

      ElMessage.success('订单提交并付款成功！')

      // 重新获取购物车数据（后端已经删除了选中的商品）
      await cartStore.fetchCart()

      // 跳转到我的订单列表（显示已付款订单）
      router.push(`/orders?status=PAID`)

    } catch (error: any) {
      if (error !== 'cancel') {
        console.error('提交订单失败:', error)
        ElMessage.error(error.response?.data?.message || '提交订单失败')
      }
    } finally {
      submitting.value = false
    }
  }

  // 页面初始化
  onMounted(() => {
    // 如果没有选中的商品，返回购物车
    if (selectedItems.value.length === 0) {
      ElMessage.warning('请先选择要结算的商品')
      router.push('/cart')
      return
    }

    // 获取用户地址列表
    fetchAddresses()
  })
</script>

<style scoped>
  .checkout-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
  }

  .checkout-header {
    margin-bottom: 30px;
  }

  .checkout-header h1 {
    margin: 0 0 10px 0;
    font-size: 28px;
    color: #303133;
  }

  .checkout-section {
    background: white;
    border-radius: 8px;
    padding: 24px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .checkout-section h2 {
    margin: 0 0 20px 0;
    font-size: 18px;
    color: #303133;
    border-bottom: 2px solid #409eff;
    padding-bottom: 8px;
  }

  .address-selector {
    margin-bottom: 24px;
  }

  .address-selector h3 {
    margin: 0 0 16px 0;
    font-size: 16px;
    color: #606266;
  }

  .address-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  .address-item {
    width: 100%;
    margin: 0;
  }

  .address-item :deep(.el-radio__label) {
    width: 100%;
    padding-left: 8px;
  }

  .address-content {
    border: 1px solid #dcdfe6;
    border-radius: 6px;
    padding: 16px;
    transition: all 0.3s;
    cursor: pointer;
  }

  .address-item.is-checked .address-content {
    border-color: #409eff;
    background-color: #f0f9ff;
  }

  .address-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 8px;
  }

  .receiver-name {
    font-weight: 600;
    color: #303133;
  }

  .receiver-phone {
    color: #606266;
  }

  .address-detail {
    color: #909399;
    font-size: 14px;
    line-height: 1.4;
  }

  .order-summary {
    background: white;
    border-radius: 8px;
    padding: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 20px;
  }

  .order-summary h2 {
    margin: 0 0 20px 0;
    font-size: 18px;
    color: #303133;
    border-bottom: 2px solid #409eff;
    padding-bottom: 8px;
  }

  .order-items {
    margin-bottom: 20px;
  }

  .order-item {
    display: flex;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;
  }

  .order-item:last-child {
    border-bottom: none;
  }

  .item-image {
    width: 50px;
    height: 60px;
    margin-right: 12px;
    flex-shrink: 0;
  }

  .item-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 4px;
  }

  .item-info {
    flex: 1;
    min-width: 0;
  }

  .item-title {
    font-size: 14px;
    color: #303133;
    margin-bottom: 4px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .item-author {
    font-size: 12px;
    color: #909399;
    margin-bottom: 4px;
  }

  .item-price {
    font-size: 12px;
    color: #606266;
  }

  .item-total {
    font-size: 14px;
    color: #e6a23c;
    font-weight: 500;
  }

  .price-summary {
    border-top: 1px solid #f0f0f0;
    padding-top: 16px;
    margin-bottom: 20px;
  }

  .price-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    font-size: 14px;
  }

  .price-row.total {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    border-top: 1px solid #f0f0f0;
    padding-top: 8px;
    margin-top: 8px;
  }

  .total-amount {
    color: #e6a23c;
    font-size: 18px;
  }

  .checkout-actions {
    display: flex;
    gap: 12px;
  }

  .submit-btn {
    flex: 1;
  }

  :deep(.el-radio) {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    padding: 12px;
    border: 1px solid #dcdfe6;
    border-radius: 6px;
    transition: all 0.3s;
  }

  :deep(.el-radio:hover) {
    border-color: #409eff;
  }

  :deep(.el-radio.is-checked) {
    border-color: #409eff;
    background-color: #f0f9ff;
  }

  :deep(.el-radio__label) {
    display: flex;
    align-items: center;
    gap: 8px;
  }
</style>
