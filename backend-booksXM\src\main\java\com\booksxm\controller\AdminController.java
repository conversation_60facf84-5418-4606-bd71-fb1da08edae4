package com.booksxm.controller;

import com.booksxm.entity.User;
import com.booksxm.service.UserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/admin")
@CrossOrigin(origins = "*", maxAge = 3600)
public class AdminController {

    private static final Logger logger = LoggerFactory.getLogger(AdminController.class);

    @Autowired
    private UserService userService;

    /**
     * 获取用户列表（管理员功能）
     */
    @GetMapping("/users")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> getUsers(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(defaultValue = "role") String sortBy,
            @RequestParam(defaultValue = "asc") String sortDir,
            @RequestParam(required = false) String username,
            @RequestParam(required = false) String email,
            @RequestParam(required = false) User.Role role,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startDate,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endDate) {
        try {
            // 创建复合排序：先按角色排序（ADMIN在前），再按创建时间排序
            Sort sort;
            if ("role".equals(sortBy)) {
                // 角色排序：ADMIN在前，USER在后，然后按创建时间倒序
                // 注意：枚举中USER在ADMIN前面，所以用desc让ADMIN排在前面
                sort = Sort.by(
                        Sort.Order.desc("role"), // USER, ADMIN -> ADMIN, USER (降序)
                        Sort.Order.desc("createdAt") // 创建时间倒序
                );
            } else {
                // 其他字段排序，但仍然优先按角色分组
                Sort.Direction direction = sortDir.equalsIgnoreCase("desc") ? Sort.Direction.DESC : Sort.Direction.ASC;
                sort = Sort.by(
                        Sort.Order.desc("role"), // 先按角色降序排序，ADMIN在前
                        new Sort.Order(direction, sortBy) // 再按指定字段排序
                );
            }
            Pageable pageable = PageRequest.of(page, size, sort);

            Page<User> users = userService.getUsersWithFilters(
                    username,
                    email,
                    role,
                    startDate,
                    endDate,
                    pageable);

            Map<String, Object> response = new HashMap<>();
            response.put("users", users.getContent());
            response.put("currentPage", users.getNumber());
            response.put("totalItems", users.getTotalElements());
            response.put("totalPages", users.getTotalPages());
            response.put("hasNext", users.hasNext());
            response.put("hasPrevious", users.hasPrevious());

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("获取用户列表失败: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(Map.of("message", "获取用户列表失败: " + e.getMessage()));
        }
    }

    /**
     * 更新用户状态（管理员功能）
     */
    @PutMapping("/users/{id}/status")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> updateUserStatus(@PathVariable Long id, @RequestBody Map<String, Object> request) {
        try {
            Boolean isActive = (Boolean) request.get("isActive");
            if (isActive == null) {
                return ResponseEntity.badRequest()
                        .body(Map.of("message", "状态不能为空"));
            }

            User user = userService.updateUserStatus(id, isActive);
            return ResponseEntity.ok(user);
        } catch (Exception e) {
            logger.error("更新用户状态失败: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(Map.of("message", "更新用户状态失败: " + e.getMessage()));
        }
    }

    /**
     * 更新用户角色（管理员功能）
     */
    @PutMapping("/users/{id}/role")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> updateUserRole(@PathVariable Long id, @RequestBody Map<String, String> request) {
        try {
            String roleStr = request.get("role");
            if (roleStr == null || roleStr.trim().isEmpty()) {
                return ResponseEntity.badRequest()
                        .body(Map.of("message", "角色不能为空"));
            }

            User.Role role;
            try {
                role = User.Role.valueOf(roleStr.toUpperCase());
            } catch (IllegalArgumentException e) {
                return ResponseEntity.badRequest()
                        .body(Map.of("message", "无效的用户角色"));
            }

            User user = userService.updateUserRole(id, role);
            return ResponseEntity.ok(user);
        } catch (Exception e) {
            logger.error("更新用户角色失败: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(Map.of("message", "更新用户角色失败: " + e.getMessage()));
        }
    }

    /**
     * 重置用户密码（管理员功能）
     */
    @PutMapping("/users/{id}/reset-password")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> resetUserPassword(@PathVariable Long id) {
        try {
            String newPassword = userService.resetUserPassword(id);
            return ResponseEntity.ok(Map.of(
                    "message", "密码重置成功",
                    "newPassword", newPassword));
        } catch (Exception e) {
            logger.error("重置用户密码失败: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(Map.of("message", "重置用户密码失败: " + e.getMessage()));
        }
    }

    /**
     * 获取仪表盘数据（管理员功能）
     */
    @GetMapping("/dashboard")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> getDashboard() {
        try {
            Map<String, Object> dashboard = userService.getDashboardStats();
            return ResponseEntity.ok(dashboard);
        } catch (Exception e) {
            logger.error("获取仪表盘数据失败: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(Map.of("message", "获取仪表盘数据失败: " + e.getMessage()));
        }
    }
}
