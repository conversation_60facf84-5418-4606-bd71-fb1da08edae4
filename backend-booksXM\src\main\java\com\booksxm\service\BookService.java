package com.booksxm.service;

import com.booksxm.entity.Book;
import com.booksxm.repository.BookRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Service
@Transactional
public class BookService {

    private static final Logger logger = LoggerFactory.getLogger(BookService.class);

    @Autowired
    private BookRepository bookRepository;

    /**
     * 获取所有图书（分页）
     */
    @Transactional(readOnly = true)
    public Page<Book> getAllBooks(Pageable pageable) {
        return bookRepository.findByIsActive(true, pageable);
    }

    /**
     * 根据ID获取图书
     */
    @Transactional(readOnly = true)
    public Optional<Book> getBookById(Long id) {
        Optional<Book> book = bookRepository.findById(id);
        if (book.isPresent() && book.get().getIsActive()) {
            // 增加浏览次数
            book.get().increaseViewCount();
            bookRepository.save(book.get());
        }
        return book;
    }

    /**
     * 根据分类获取图书
     */
    @Transactional(readOnly = true)
    public Page<Book> getBooksByCategory(String category, Pageable pageable) {
        return bookRepository.findByCategoryAndIsActive(category, true, pageable);
    }

    /**
     * 搜索图书
     */
    @Transactional(readOnly = true)
    public Page<Book> searchBooks(String keyword, Pageable pageable) {
        return bookRepository.searchBooks(keyword, pageable);
    }

    /**
     * 高级搜索图书
     */
    @Transactional(readOnly = true)
    public Page<Book> searchBooksWithFilters(String title, String author, String category,
            BigDecimal minPrice, BigDecimal maxPrice,
            Boolean inStock, Pageable pageable) {
        return bookRepository.findBooksWithFilters(title, author, category,
                minPrice, maxPrice, inStock, pageable);
    }

    /**
     * 获取推荐图书
     */
    @Transactional(readOnly = true)
    public List<Book> getFeaturedBooks() {
        return bookRepository.findByIsFeaturedTrueAndIsActiveTrueOrderByCreatedAtDesc();
    }

    /**
     * 获取热销图书
     */
    @Transactional(readOnly = true)
    public List<Book> getHotBooks() {
        return bookRepository.findTop10ByIsActiveTrueOrderBySalesCountDesc();
    }

    /**
     * 获取最新图书
     */
    @Transactional(readOnly = true)
    public List<Book> getLatestBooks() {
        return bookRepository.findTop10ByIsActiveTrueOrderByCreatedAtDesc();
    }

    /**
     * 获取高评分图书
     */
    @Transactional(readOnly = true)
    public List<Book> getHighRatedBooks() {
        return bookRepository.findTop10ByIsActiveTrueAndRatingGreaterThanOrderByRatingDesc(4.0);
    }

    /**
     * 获取所有分类
     */
    @Transactional(readOnly = true)
    public List<String> getAllCategories() {
        return bookRepository.findAllCategories();
    }

    /**
     * 创建图书（管理员功能）
     */
    public Book createBook(Book book) {
        try {
            // 处理ISBN字段，避免空值冲突
            if (book.getIsbn() == null || book.getIsbn().trim().isEmpty()) {
                // 如果ISBN为空，生成一个唯一的ISBN
                book.setIsbn("AUTO-" + System.currentTimeMillis());
            } else {
                // 检查ISBN是否已存在
                if (bookRepository.existsByIsbn(book.getIsbn())) {
                    throw new RuntimeException("ISBN已存在: " + book.getIsbn());
                }
            }

            book.setIsActive(true);

            // 设置默认值
            if (book.getRating() == null) {
                book.setRating(0.0);
            }
            if (book.getRatingCount() == null) {
                book.setRatingCount(0);
            }
            if (book.getSalesCount() == null) {
                book.setSalesCount(0);
            }
            if (book.getViewCount() == null) {
                book.setViewCount(0);
            }
            if (book.getStock() == null) {
                book.setStock(0);
            }

            // 强制设置创建时间和更新时间为当前时间
            LocalDateTime now = LocalDateTime.now();
            book.setCreatedAt(now);
            book.setUpdatedAt(now);

            Book savedBook = bookRepository.save(book);
            logger.info("创建图书成功: {}", savedBook.getTitle());
            return savedBook;
        } catch (Exception e) {
            logger.error("创建图书失败: {}", e.getMessage());
            throw new RuntimeException("创建图书失败: " + e.getMessage());
        }
    }

    /**
     * 更新图书（管理员功能）
     */
    public Book updateBook(Long id, Book bookDetails) {
        try {
            Book book = bookRepository.findById(id)
                    .orElseThrow(() -> new RuntimeException("图书不存在"));

            // 更新图书信息
            book.setTitle(bookDetails.getTitle());
            book.setAuthor(bookDetails.getAuthor());
            book.setIsbn(bookDetails.getIsbn());
            book.setPublisher(bookDetails.getPublisher());
            book.setPublishDate(bookDetails.getPublishDate());
            book.setPrice(bookDetails.getPrice());
            book.setOriginalPrice(bookDetails.getOriginalPrice());
            book.setStock(bookDetails.getStock());
            book.setDescription(bookDetails.getDescription());
            book.setCategory(bookDetails.getCategory());
            book.setPageCount(bookDetails.getPageCount());
            book.setLanguage(bookDetails.getLanguage());
            book.setIsFeatured(bookDetails.getIsFeatured());

            if (bookDetails.getCoverUrl() != null) {
                book.setCoverUrl(bookDetails.getCoverUrl());
            }

            Book updatedBook = bookRepository.save(book);
            logger.info("更新图书成功: {}", updatedBook.getTitle());
            return updatedBook;
        } catch (Exception e) {
            logger.error("更新图书失败: {}", e.getMessage());
            throw new RuntimeException("更新图书失败: " + e.getMessage());
        }
    }

    /**
     * 删除图书（软删除）
     */
    public void deleteBook(Long id) {
        try {
            Book book = bookRepository.findById(id)
                    .orElseThrow(() -> new RuntimeException("图书不存在"));

            book.setIsActive(false);
            bookRepository.save(book);
            logger.info("删除图书成功: {}", book.getTitle());
        } catch (Exception e) {
            logger.error("删除图书失败: {}", e.getMessage());
            throw new RuntimeException("删除图书失败: " + e.getMessage());
        }
    }

    /**
     * 更新图书库存
     */
    public void updateStock(Long bookId, int quantity) {
        try {
            Book book = bookRepository.findById(bookId)
                    .orElseThrow(() -> new RuntimeException("图书不存在"));

            if (quantity < 0) {
                book.decreaseStock(-quantity);
            } else {
                book.increaseStock(quantity);
            }

            bookRepository.save(book);
            logger.info("更新图书库存成功: {} -> {}", book.getTitle(), book.getStock());
        } catch (Exception e) {
            logger.error("更新图书库存失败: {}", e.getMessage());
            throw new RuntimeException("更新图书库存失败: " + e.getMessage());
        }
    }

    /**
     * 检查图书是否有足够库存
     */
    @Transactional(readOnly = true)
    public boolean checkStock(Long bookId, int quantity) {
        Optional<Book> book = bookRepository.findById(bookId);
        return book.isPresent() && book.get().getStock() >= quantity;
    }

    /**
     * 获取库存不足的图书
     */
    @Transactional(readOnly = true)
    public List<Book> getLowStockBooks(int threshold) {
        return bookRepository.findAll().stream()
                .filter(book -> book.getIsActive() && book.getStock() <= threshold)
                .toList();
    }
}
