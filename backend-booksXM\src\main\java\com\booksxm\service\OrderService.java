package com.booksxm.service;

import com.booksxm.entity.Order;
import com.booksxm.entity.OrderItem;
import com.booksxm.entity.User;
import com.booksxm.entity.Book;
import com.booksxm.entity.CartItem;
import com.booksxm.repository.OrderRepository;
import com.booksxm.repository.UserRepository;
import com.booksxm.repository.BookRepository;
import com.booksxm.repository.CartItemRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Service
@Transactional
public class OrderService {

    private static final Logger logger = LoggerFactory.getLogger(OrderService.class);

    @Autowired
    private OrderRepository orderRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private BookRepository bookRepository;

    @Autowired
    private CartItemRepository cartItemRepository;

    /**
     * 获取所有订单（管理员功能）
     */
    @Transactional(readOnly = true)
    public Page<Order> getAllOrders(Pageable pageable) {
        return orderRepository.findAll(pageable);
    }

    /**
     * 根据条件查询订单
     */
    @Transactional(readOnly = true)
    public Page<Order> getOrdersWithFilters(Long userId, Order.OrderStatus status,
            Order.PaymentStatus paymentStatus,
            LocalDateTime startDate, LocalDateTime endDate,
            String orderNumber, String username,
            Pageable pageable) {
        // 如果有用户名参数，先查找用户ID
        if (username != null && !username.trim().isEmpty()) {
            Optional<User> user = userRepository.findByUsername(username.trim());
            if (user.isPresent()) {
                userId = user.get().getId();
            } else {
                // 用户不存在，返回空结果
                return Page.empty(pageable);
            }
        }

        return orderRepository.findOrdersWithFilters(userId, status, paymentStatus,
                startDate, endDate, orderNumber, pageable);
    }

    /**
     * 获取用户订单
     */
    @Transactional(readOnly = true)
    public Page<Order> getUserOrders(Long userId, Pageable pageable) {
        return orderRepository.findByUserId(userId, pageable);
    }

    /**
     * 根据状态获取用户订单
     */
    @Transactional(readOnly = true)
    public Page<Order> getUserOrdersByStatus(Long userId, Order.OrderStatus status, Pageable pageable) {
        return orderRepository.findByUserIdAndStatus(userId, status, pageable);
    }

    /**
     * 根据ID获取订单
     */
    @Transactional(readOnly = true)
    public Order getOrderById(Long id) {
        return orderRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("订单不存在"));
    }

    /**
     * 根据订单号获取订单
     */
    @Transactional(readOnly = true)
    public Order getOrderByOrderNumber(String orderNumber) {
        return orderRepository.findByOrderNumber(orderNumber)
                .orElseThrow(() -> new RuntimeException("订单不存在"));
    }

    /**
     * 创建订单
     */
    public Order createOrder(Long userId, Map<String, Object> orderData) {
        try {
            User user = userRepository.findById(userId)
                    .orElseThrow(() -> new RuntimeException("用户不存在"));

            // 获取用户选中的购物车项目
            List<CartItem> selectedItems = cartItemRepository.findByUserIdAndIsSelected(userId, true);
            if (selectedItems.isEmpty()) {
                throw new RuntimeException("购物车中没有选中的商品");
            }

            // 创建订单
            Order order = new Order();
            order.setUser(user);
            order.setStatus(Order.OrderStatus.PENDING);
            order.setPaymentStatus(Order.PaymentStatus.UNPAID);

            // 设置收货信息
            if (orderData.containsKey("receiverName")) {
                order.setReceiverName((String) orderData.get("receiverName"));
            }
            if (orderData.containsKey("receiverPhone")) {
                order.setReceiverPhone((String) orderData.get("receiverPhone"));
            }
            if (orderData.containsKey("shippingAddress")) {
                order.setShippingAddress((String) orderData.get("shippingAddress"));
            }
            if (orderData.containsKey("notes")) {
                order.setNotes((String) orderData.get("notes"));
            }

            // 计算订单金额
            BigDecimal totalAmount = BigDecimal.ZERO;
            for (CartItem cartItem : selectedItems) {
                Book book = cartItem.getBook();

                // 检查库存
                if (book.getStock() < cartItem.getQuantity()) {
                    throw new RuntimeException("商品 " + book.getTitle() + " 库存不足");
                }

                BigDecimal itemTotal = book.getPrice().multiply(BigDecimal.valueOf(cartItem.getQuantity()));
                totalAmount = totalAmount.add(itemTotal);
            }

            order.setTotalAmount(totalAmount);
            order.setShippingFee(BigDecimal.ZERO); // 暂时免运费
            order.setDiscountAmount(BigDecimal.ZERO);
            order.calculateFinalAmount();

            // 生成订单号
            order.setOrderNumber("BX" + System.currentTimeMillis());

            // 保存订单
            Order savedOrder = orderRepository.save(order);

            // 创建订单项目并减少库存
            for (CartItem cartItem : selectedItems) {
                Book book = cartItem.getBook();

                OrderItem orderItem = new OrderItem(savedOrder, book, cartItem.getQuantity(), book.getPrice());
                savedOrder.getOrderItems().add(orderItem);

                // 减少库存
                book.decreaseStock(cartItem.getQuantity());
                bookRepository.save(book);
            }

            // 清空选中的购物车项目
            cartItemRepository.deleteAll(selectedItems);

            logger.info("创建订单成功: {}", savedOrder.getOrderNumber());
            return savedOrder;

        } catch (Exception e) {
            logger.error("创建订单失败: {}", e.getMessage());
            throw new RuntimeException("创建订单失败: " + e.getMessage());
        }
    }

    /**
     * 更新订单状态
     */
    public Order updateOrderStatus(Long orderId, Order.OrderStatus status, String trackingNumber) {
        try {
            Order order = getOrderById(orderId);

            Order.OrderStatus oldStatus = order.getStatus();
            order.setStatus(status);

            switch (status) {
                case PENDING:
                    // 订单创建，无需额外操作
                    break;
                case PAID:
                    order.setPaymentStatus(Order.PaymentStatus.PAID);
                    order.setPaymentTime(LocalDateTime.now());
                    break;
                case SHIPPED:
                    if (!order.canBeShipped()) {
                        throw new RuntimeException("订单状态不允许发货");
                    }
                    order.setShippingTime(LocalDateTime.now());
                    if (trackingNumber != null && !trackingNumber.trim().isEmpty()) {
                        order.setTrackingNumber(trackingNumber.trim());
                    }
                    break;
                case DELIVERED:
                    if (!order.canBeDelivered()) {
                        throw new RuntimeException("订单状态不允许确认收货");
                    }
                    order.setDeliveryTime(LocalDateTime.now());
                    break;
                case CANCELLED:
                    if (!order.canBeCancelled()) {
                        throw new RuntimeException("订单状态不允许取消");
                    }
                    // 恢复库存
                    restoreStock(order);
                    break;
                case REFUNDED:
                    order.setPaymentStatus(Order.PaymentStatus.REFUNDED);
                    // 恢复库存
                    restoreStock(order);
                    break;
            }

            Order updatedOrder = orderRepository.save(order);
            logger.info("更新订单状态成功: {} {} -> {}", order.getOrderNumber(), oldStatus, status);
            return updatedOrder;

        } catch (Exception e) {
            logger.error("更新订单状态失败: {}", e.getMessage());
            throw new RuntimeException("更新订单状态失败: " + e.getMessage());
        }
    }

    /**
     * 取消订单
     */
    public Order cancelOrder(Long orderId) {
        return updateOrderStatus(orderId, Order.OrderStatus.CANCELLED, null);
    }

    /**
     * 模拟支付
     */
    public Order simulatePayment(Long orderId, Order.PaymentMethod paymentMethod) {
        try {
            Order order = orderRepository.findById(orderId)
                    .orElseThrow(() -> new RuntimeException("订单不存在"));

            // 检查订单状态
            if (order.getStatus() != Order.OrderStatus.PENDING) {
                throw new RuntimeException("订单状态不允许支付");
            }

            if (order.getPaymentStatus() != Order.PaymentStatus.UNPAID) {
                throw new RuntimeException("订单已支付或状态异常");
            }

            // 更新订单状态和支付信息
            order.setStatus(Order.OrderStatus.PAID);
            order.setPaymentStatus(Order.PaymentStatus.PAID);
            order.setPaymentMethod(paymentMethod);
            order.setPaymentTime(LocalDateTime.now());

            Order updatedOrder = orderRepository.save(order);
            logger.info("模拟支付成功: {} 支付方式: {}", order.getOrderNumber(), paymentMethod);
            return updatedOrder;

        } catch (Exception e) {
            logger.error("模拟支付失败: {}", e.getMessage());
            throw new RuntimeException("支付失败: " + e.getMessage());
        }
    }

    /**
     * 删除订单（管理员功能）
     */
    public void deleteOrder(Long orderId) {
        try {
            Order order = getOrderById(orderId);

            // 如果订单未取消，先恢复库存
            if (order.getStatus() != Order.OrderStatus.CANCELLED &&
                    order.getStatus() != Order.OrderStatus.REFUNDED) {
                restoreStock(order);
            }

            orderRepository.delete(order);
            logger.info("删除订单成功: {}", order.getOrderNumber());

        } catch (Exception e) {
            logger.error("删除订单失败: {}", e.getMessage());
            throw new RuntimeException("删除订单失败: " + e.getMessage());
        }
    }

    /**
     * 恢复库存
     */
    private void restoreStock(Order order) {
        for (OrderItem item : order.getOrderItems()) {
            Book book = item.getBook();
            book.increaseStock(item.getQuantity());
            bookRepository.save(book);
        }
    }

    /**
     * 获取订单统计信息
     */
    @Transactional(readOnly = true)
    public Map<String, Object> getOrderStats() {
        Map<String, Object> stats = new HashMap<>();

        // 总订单数
        long totalOrders = orderRepository.count();
        stats.put("totalOrders", totalOrders);

        // 各状态订单数
        for (Order.OrderStatus status : Order.OrderStatus.values()) {
            long count = orderRepository.countByStatus(status);
            stats.put(status.name().toLowerCase() + "Orders", count);
        }

        // 今日订单数
        LocalDateTime startOfDay = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0).withNano(0);
        LocalDateTime endOfDay = startOfDay.plusDays(1);
        long todayOrders = orderRepository.countOrdersByCreatedAtBetween(startOfDay, endOfDay);
        stats.put("todayOrders", todayOrders);

        // 本月订单数
        LocalDateTime startOfMonth = LocalDateTime.now().withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0)
                .withNano(0);
        long monthOrders = orderRepository.countOrdersByCreatedAtBetween(startOfMonth, LocalDateTime.now());
        stats.put("monthOrders", monthOrders);

        return stats;
    }

    /**
     * 发货
     */
    public Order shipOrder(Long orderId, String trackingNumber) {
        try {
            Order order = orderRepository.findById(orderId)
                    .orElseThrow(() -> new RuntimeException("订单不存在"));

            // 检查订单状态
            if (order.getStatus() != Order.OrderStatus.PAID) {
                throw new RuntimeException("只有已付款的订单才能发货");
            }

            // 更新订单状态
            order.setStatus(Order.OrderStatus.SHIPPED);
            order.setShippingTime(LocalDateTime.now());

            if (trackingNumber != null && !trackingNumber.trim().isEmpty()) {
                order.setTrackingNumber(trackingNumber.trim());
            }

            Order savedOrder = orderRepository.save(order);
            logger.info("订单发货成功: {}, 物流单号: {}", order.getOrderNumber(), trackingNumber);

            return savedOrder;
        } catch (Exception e) {
            logger.error("发货失败: {}", e.getMessage());
            throw new RuntimeException("发货失败: " + e.getMessage());
        }
    }

    /**
     * 确认收货
     */
    public Order confirmOrder(Long orderId) {
        try {
            Order order = orderRepository.findById(orderId)
                    .orElseThrow(() -> new RuntimeException("订单不存在"));

            // 检查订单状态
            if (order.getStatus() != Order.OrderStatus.SHIPPED) {
                throw new RuntimeException("只有已发货的订单才能确认收货");
            }

            // 更新订单状态为已送达
            order.setStatus(Order.OrderStatus.DELIVERED);
            order.setDeliveryTime(LocalDateTime.now());

            // 更新图书销售数量
            for (OrderItem item : order.getOrderItems()) {
                Book book = item.getBook();
                // 确保 salesCount 不为 null
                if (book.getSalesCount() == null) {
                    book.setSalesCount(0);
                }
                book.setSalesCount(book.getSalesCount() + item.getQuantity());
                bookRepository.save(book);
            }

            Order savedOrder = orderRepository.save(order);
            logger.info("确认收货成功: {}", order.getOrderNumber());

            return savedOrder;
        } catch (Exception e) {
            logger.error("确认收货失败: {}", e.getMessage());
            throw new RuntimeException("确认收货失败: " + e.getMessage());
        }
    }
}
